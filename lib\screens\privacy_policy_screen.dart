import 'package:flutter/material.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        backgroundColor: const Color(0xFFFF5C22), // Chica Orange
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Text(
                    'CHICA\'S CHICKEN',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFFFF5C22),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Privacy Policy',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Last Updated: ${DateTime.now().toString().split(' ')[0]}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Content sections
            _buildSection(
              context,
              '1. INTRODUCTION',
              'CHICA\'S Chicken ("we," "our," or "us") respects your privacy and is committed to protecting your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application, website, or services.\n\nThis policy complies with Canadian privacy laws, including the Personal Information Protection and Electronic Documents Act (PIPEDA).',
            ),

            _buildSection(
              context,
              '2. INFORMATION WE COLLECT',
              'We may collect the following types of information:\n\n• Personal Information: Name, email address, phone number, date of birth\n• Account Information: Username, password, preferences\n• Payment Information: Credit card details, billing address\n• Location Information: GPS location for delivery services\n• Device Information: Device type, operating system, unique identifiers\n• Usage Information: App usage patterns, preferences, order history',
            ),

            _buildSection(
              context,
              '3. HOW WE COLLECT INFORMATION',
              'We collect information through:\n• Direct provision by you (registration, orders, surveys)\n• Automatic collection (cookies, analytics, device data)\n• Third-party sources (payment processors, delivery partners)\n• Social media platforms (if you connect your accounts)\n• Location services (with your permission)',
            ),

            _buildSection(
              context,
              '4. HOW WE USE YOUR INFORMATION',
              'We use your information to:\n• Process and fulfill your orders\n• Provide customer service and support\n• Operate our loyalty rewards program\n• Send promotional offers and communications\n• Improve our services and user experience\n• Comply with legal obligations\n• Prevent fraud and ensure security',
            ),

            _buildSection(
              context,
              '5. INFORMATION SHARING',
              'We may share your information with:\n• Service providers (payment processors, delivery partners)\n• Business partners (with your consent)\n• Legal authorities (when required by law)\n• Corporate affiliates\n• Third parties in business transactions\n\nWe do not sell your personal information to third parties.',
            ),

            _buildSection(
              context,
              '6. DATA SECURITY',
              'We implement appropriate security measures to protect your information:\n• Encryption of sensitive data\n• Secure servers and networks\n• Regular security assessments\n• Employee training on privacy practices\n• Access controls and authentication\n\nHowever, no method of transmission is 100% secure.',
            ),

            _buildSection(
              context,
              '7. YOUR PRIVACY RIGHTS',
              'Under Canadian privacy law, you have the right to:\n• Access your personal information\n• Request correction of inaccurate information\n• Request deletion of your information\n• Withdraw consent for certain uses\n• File a complaint with privacy authorities\n• Receive information about our privacy practices\n\nTo exercise these rights, contact us using the information below.',
            ),

            _buildSection(
              context,
              '8. COOKIES AND TRACKING',
              'We use cookies and similar technologies to:\n• Remember your preferences\n• Analyze usage patterns\n• Provide personalized content\n• Improve our services\n\nYou can control cookies through your browser settings, but this may affect functionality.',
            ),

            _buildSection(
              context,
              '9. CHILDREN\'S PRIVACY',
              'Our services are not intended for children under 13. We do not knowingly collect personal information from children under 13. If we become aware that we have collected such information, we will delete it promptly.',
            ),

            _buildSection(
              context,
              '10. DATA RETENTION',
              'We retain your information for as long as necessary to:\n• Provide our services\n• Comply with legal obligations\n• Resolve disputes\n• Enforce our agreements\n\nWe will delete or anonymize your information when it is no longer needed.',
            ),

            _buildSection(
              context,
              '11. INTERNATIONAL TRANSFERS',
              'Your information may be transferred to and processed in countries other than Canada. We ensure appropriate safeguards are in place to protect your information during such transfers.',
            ),

            _buildSection(
              context,
              '12. CHANGES TO THIS POLICY',
              'We may update this Privacy Policy from time to time. We will notify you of significant changes through our app, website, or by email. Your continued use of our services after changes constitutes acceptance of the updated policy.',
            ),

            _buildSection(
              context,
              '13. CONTACT US',
              'If you have questions about this Privacy Policy or our privacy practices, please contact us:\n\nCHICA\'S Chicken Privacy Officer\nEmail: <EMAIL>\nPhone: 1-800-CHICAS-1\n\nMailing Address:\nCHICA\'S Chicken Privacy Department\n[Address to be provided]\nCanada\n\nFor privacy complaints, you may also contact the Office of the Privacy Commissioner of Canada.',
            ),

            const SizedBox(height: 32),

            // Footer
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: const Color(0xFFFF5C22).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Your privacy is important to us. By using CHICA\'S Chicken services, you acknowledge that you have read and understood this Privacy Policy.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFFFF5C22),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.6,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }
}
