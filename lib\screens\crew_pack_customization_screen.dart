import 'package:flutter/material.dart';
import '../models/menu_item.dart';
import '../services/menu_service.dart';
import '../widgets/sauce_selection_dialog.dart';
import '../models/crew_pack_selection.dart';
import '../widgets/custom_bottom_nav_bar.dart';

class CrewPackCustomizationScreen extends StatefulWidget {
  final MenuItem crewPack;

  const CrewPackCustomizationScreen({Key? key, required this.crewPack}) : super(key: key);

  @override
  State<CrewPackCustomizationScreen> createState() => _CrewPackCustomizationScreenState();
}

class _CrewPackCustomizationScreenState extends State<CrewPackCustomizationScreen> {
  final MenuService _menuService = MenuService();
  final Map<String, List<MenuItem>> _categoryItems = {};
  final Map<String, List<dynamic>> _selectedItems = {};
  final Map<String, CrewPackCustomization> _crewPackSelections = {};
  final List<String> _availableBuns = ['Regular Bun', 'Brioche Bun'];
  final List<String> _availableHeatLevels = ['Plain', 'Mild', 'Medium', 'Hot', 'HOT AF'];

  @override
  void initState() {
    super.initState();
    _loadCategoryItems();
    _initializeSelections();
  }

  void _initializeSelections() {
    if (widget.crewPack.customizationCounts != null) {
      for (var entry in widget.crewPack.customizationCounts!.entries) {
        _selectedItems[entry.key] = [];
        if (entry.key == 'Sandwiches') {
          _crewPackSelections[entry.key] = CrewPackCustomization(
            maxSelections: entry.value,
            crewPackType: widget.crewPack.name,  // Pass the crew pack name
          );
        }
      }
    }
  }

  Future<void> _loadCategoryItems() async {
    for (String category in widget.crewPack.customizationCategories ?? []) {
      final items = await _menuService.getMenuItems(category);
      _categoryItems[category] = items;
    }
    setState(() {});
  }

  bool _canAddMore(String category) {
    if (category == 'Sandwiches') {
      return _crewPackSelections[category]?.canAddMore() ?? false;
    }
    final maxCount = widget.crewPack.customizationCounts?[category] ?? 0;
    final currentCount = _selectedItems[category]?.length ?? 0;
    return currentCount < maxCount;
  }

  Future<void> _selectOptions(String category, MenuItem item) async {
    String? selectedBun;
    String? selectedHeatLevel;

    if (category == 'Sandwiches') {
      selectedBun = await showDialog<String>(
        context: context,
        builder: (BuildContext context) {
          return SimpleDialog(
            title: Text('Select Bun Type for ${item.name}'),
            children: _availableBuns.map((bun) {
              return SimpleDialogOption(
                onPressed: () {
                  Navigator.pop(context, bun);
                },
                child: Text(bun == 'Brioche Bun' ? 'Brioche Bun (+\$1.00)' : bun),
              );
            }).toList(),
          );
        },
      );
    }

  // Show heat level for any item that allows it
  if (item.allowsHeatLevelSelection == true) {
      // Heat level details for icon and description
      final heatLevelDetails = {
        'Plain': {
          'flames': 0,
          'color': Colors.grey,
          'desc': 'No Spice',
        },
        'Mild': {
          'flames': 1,
          'color': Colors.green,
          'desc': 'No Heat',
        },
        'Medium': {
          'flames': 3,
          'color': Colors.orange,
          'desc': 'Hot',
        },
        'Hot': {
          'flames': 4,
          'color': Colors.red,
          'desc': 'Very Hot',
        },
        'HOT AF': {
          'flames': 5,
          'color': Colors.red,
          'desc': 'Extremely Hot',
        },
      };

      selectedHeatLevel = await showDialog<String>(
        context: context,
        builder: (BuildContext context) {
          return SimpleDialog(
            title: const Row(
              children: [
                Icon(Icons.local_fire_department, color: Colors.deepOrange),
                SizedBox(width: 8),
                Text('Select Heat Level'),
              ],
            ),
            children: _availableHeatLevels.map((heatLevel) {
              final details = heatLevelDetails[heatLevel] ?? {};
              final int flames = (details['flames'] ?? 0) as int;
              final Color color = (details['color'] ?? Colors.grey) as Color;
              final String desc = (details['desc'] ?? '') as String;
              return SimpleDialogOption(
                onPressed: () {
                  Navigator.pop(context, heatLevel);
                },
                child: Row(
                  children: [
                    // Flame icon and color
                    Icon(Icons.local_fire_department, color: color),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            heatLevel.toUpperCase(),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            desc,
                            style: const TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                    // Flame rating
                    Row(
                      children: List.generate(5, (i) => Icon(
                        Icons.local_fire_department,
                        color: i < flames ? Colors.red : Colors.grey[300],
                        size: 18,
                      )),
                    ),
                  ],
                ),
              );
            }).toList(),
          );
        },
      );
    }

    setState(() {
      // Always set bunPrice to 0.0 for crew pack sandwiches
      double bunPrice = 0.0;
      String? heatLevel = selectedHeatLevel;
      if (category == 'Sandwiches') {
        _crewPackSelections[category]?.addSelection(
          CrewPackSelection(
            sandwichId: item.id,
            bunType: selectedBun,
            heatLevel: heatLevel,
            price: 0.0, // Always 0 for crew pack sandwiches
          ),
        );
      } else if (item.allowsHeatLevelSelection == true) {
        // For any item with heat level selection, store as a map with heatLevel
        _selectedItems[category]?.add({'item': item, 'heatLevel': heatLevel});
      } else {
        _selectedItems[category]?.add(item);
      }
    });
  }

  void _addItem(String category, MenuItem item) {
    if (category == 'Sandwiches') {
      _selectOptions(category, item);
    } else if (item.allowsHeatLevelSelection == true) {
      _selectOptions(category, item);
    } else if (_canAddMore(category)) {
      setState(() {
        _selectedItems[category]?.add(item);
      });
    }
  }

  void _removeItem(String category, MenuItem item) {
    setState(() {
      final selectedList = _selectedItems[category];
      if (selectedList != null && selectedList.isNotEmpty) {
        for (int i = 0; i < selectedList.length; i++) {
          final selected = selectedList[i];
          if (selected is Map && selected['item'] is MenuItem) {
            if ((selected['item'] as MenuItem).id == item.id) {
              selectedList.removeAt(i);
              break;
            }
          } else if (selected is MenuItem && selected.id == item.id) {
            selectedList.removeAt(i);
            break;
            }
          }
        }
    });
  }

  void _removeSelection(String category, int index) {
    setState(() {
      _crewPackSelections[category]?.removeSelection(index);
    });
  }

  Future<void> _handleSauceSelection() async {
    final result = await showDialog<List<String>>(
      context: context,
      builder: (BuildContext context) => SauceSelectionDialog(
        maxSauces: widget.crewPack.includedSauceCount,
        initialSelections: widget.crewPack.selectedSauces,
      ),
    );

    if (result != null) {
      setState(() {
        widget.crewPack.selectedSauces = result;
      });
    }
  }

  bool _isValid() {
    if (widget.crewPack.customizationCounts == null) return false;

    for (var entry in widget.crewPack.customizationCounts!.entries) {
      final category = entry.key;
      final maxCount = entry.value;
      int currentCount;

      if (category == 'Sandwiches') {
        currentCount = _crewPackSelections[category]?.selections.length ?? 0;
      } else {
        currentCount = _selectedItems[category]?.length ?? 0;
      }

      if (currentCount != maxCount) {
        return false;
      }
    }

    if (widget.crewPack.allowsSauceSelection &&
        (widget.crewPack.selectedSauces == null ||
            widget.crewPack.selectedSauces!.length !=
                widget.crewPack.includedSauceCount)) {
      return false;
    }

    return true;
  }

  String? _getValidationMessage() {
    if (widget.crewPack.customizationCounts == null) {
      return 'Crew pack customization counts are not defined.';
    }

    for (var entry in widget.crewPack.customizationCounts!.entries) {
      final category = entry.key;
      final maxCount = entry.value;
      int currentCount;

      if (category == 'Sandwiches') {
        currentCount = _crewPackSelections[category]?.selections.length ?? 0;
      } else {
        currentCount = _selectedItems[category]?.length ?? 0;
      }

      if (currentCount != maxCount) {
        return 'Please select $maxCount ${category.toLowerCase()}. Currently selected: $currentCount.';
      }
    }

    if (widget.crewPack.allowsSauceSelection) {
      final selectedSauceCount = widget.crewPack.selectedSauces?.length ?? 0;
      final requiredSauceCount = widget.crewPack.includedSauceCount ?? 0;
      if (selectedSauceCount != requiredSauceCount) {
        return 'Please select $requiredSauceCount sauces. Currently selected: $selectedSauceCount.';
      }
    }

    return null; // All valid
  }

  Widget _buildCategorySection(String category) {
    final selectedCount = category == 'Sandwiches'
        ? _crewPackSelections[category]?.selections.length ?? 0
        : _selectedItems[category]?.length ?? 0;
    final maxCount = widget.crewPack.customizationCounts?[category] ?? 0;

    // Determine size label for Sides section
    String sidesSizeLabel = '';
    if (category == 'Sides') {
      if (widget.crewPack.name == 'Crew Pack 1') {
        sidesSizeLabel = ' - Regular';
      } else if (widget.crewPack.name == 'Crew Pack 2' || widget.crewPack.name == 'Crew Pack 3') {
        sidesSizeLabel = ' - Large';
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'CHOOSE $maxCount ${category.toUpperCase()}${category == 'Sides' ? sidesSizeLabel : ''}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.deepOrange,
                fontWeight: FontWeight.bold,
              ),
        ),
        Text(
          '($selectedCount/$maxCount selected)',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        const SizedBox(height: 8),
        if (category == 'Sandwiches') ...[
          if (_crewPackSelections[category]?.selections != null)
            ..._crewPackSelections[category]!.selections.asMap().entries.map((entry) {
              final index = entry.key;
              final selection = entry.value;
              final sandwich = _categoryItems[category]?.firstWhere(
                (item) => item.id == selection.sandwichId,
                orElse: () => MenuItem(
                  id: 'placeholder',
                  name: '',
                  description: '',
                  price: 0,
                  imageUrl: 'assets/placeholder.png',
                  category: '',
                ),
              );
              return ListTile(
                title: Text(sandwich?.name ?? 'Unknown Sandwich'),
                subtitle: Text('Bun: ${selection.bunType}, Heat: ${selection.heatLevel}'),
                trailing: IconButton(
                  icon: const Icon(Icons.remove_circle_outline),
                  onPressed: () => _removeSelection(category, index),
                ),
              );
            }),
          if (_canAddMore(category) && _categoryItems[category] != null)
            ..._categoryItems[category]!.map<Widget>((sandwich) {
              final itemCount = _crewPackSelections[category]?.selections
                  .where((s) => s.sandwichId == sandwich.id)
                  .length ?? 0;
              return ListTile(
                title: Text(sandwich.name),
                subtitle: Text('Selected: $itemCount'),
                trailing: ElevatedButton(
                  onPressed: _canAddMore(category)
                      ? () => _selectOptions(category, sandwich)
                      : null,
                  child: const Text('Add'),
                ),
              );
            }).toList(),
        ] else if (category == 'Chicken Bites') ...[
          ...(() {
            final selectedList = (_selectedItems[category] ?? []);
            final List<Widget> tiles = [];
            for (final item in _categoryItems[category] ?? []) {
              final isOGBites = item.name.toLowerCase().contains('og bites') || item.name.toLowerCase().contains('og chicken bites');
              if (isOGBites) {
                final ogBitesSelections = selectedList.asMap().entries.where((e) => e.value is Map && e.value['item'] is MenuItem && (e.value['item'] as MenuItem).id == item.id).toList();
                for (final entry in ogBitesSelections) {
                  final idx = entry.key;
                  final selected = entry.value;
                  final String? heatLevel = selected['heatLevel'] as String?;
                  tiles.add(ListTile(
                    title: Text(item.name),
                    subtitle: Text('Heat: ${heatLevel ?? "None"}'),
                    trailing: IconButton(
                      icon: const Icon(Icons.remove_circle_outline),
                      onPressed: () {
                        setState(() {
                          selectedList.removeAt(idx);
                        });
                      },
                    ),
                  ));
                }
                tiles.add(ListTile(
                  title: Text(item.name, style: const TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: const Text('Add OG Bites'),
                  trailing: ElevatedButton(
                    onPressed: _canAddMore(category) ? () => _addItem(category, item) : null,
                    child: const Text('Add'),
                  ),
                ));
              } else {
                final itemCount = selectedList.where((selected) {
                  if (selected is MenuItem) {
                    return selected.id == item.id;
                  } else if (selected is Map && selected['item'] is MenuItem) {
                    return (selected['item'] as MenuItem).id == item.id;
                  }
                  return false;
                }).length;
                tiles.add(ListTile(
                  title: Text(item.name),
                  subtitle: Text('Selected: $itemCount'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove_circle_outline),
                        onPressed: itemCount > 0
                            ? () => _removeItem(category, item)
                            : null,
                      ),
                      Text('$itemCount'),
                      IconButton(
                        icon: const Icon(Icons.add_circle_outline),
                        onPressed: _canAddMore(category)
                            ? () => _addItem(category, item)
                            : null,
                      ),
                    ],
                  ),
                ));
              }
            }
            return tiles;
          })(),
        ] else if (category == 'Whole Wings' || category == 'Chicken Pieces (Bone-in)') ...[
          // Display individual selected items with heat level
          ...(_selectedItems[category] ?? []).asMap().entries.map((entry) {
            final index = entry.key;
            final selected = entry.value;
            final MenuItem item = selected is Map ? selected['item'] : selected;
            final String? heatLevel = selected is Map ? selected['heatLevel'] : null;

            return ListTile(
              title: Text('${item.name}${heatLevel != null && heatLevel != "None" ? ' ($heatLevel)' : ''}'),
              subtitle: Text('Heat: ${heatLevel ?? "None"}'),
              trailing: IconButton(
                icon: const Icon(Icons.remove_circle_outline),
                onPressed: () {
                  setState(() {
                    (_selectedItems[category] as List).removeAt(index);
                  });
                },
              ),
            );
          }),
          // Display add buttons for available items
          if (_canAddMore(category) && _categoryItems[category] != null)
            ...(_categoryItems[category] ?? []).map((item) {
              final itemCount = (_selectedItems[category] ?? []).where((selected) {
                if (selected is MenuItem) {
                  return selected.id == item.id;
                } else if (selected is Map && selected['item'] is MenuItem) {
                  return (selected['item'] as MenuItem).id == item.id;
                }
                return false;
              }).length;
              return ListTile(
                title: Text(item.name),
                subtitle: Text('Selected: $itemCount'),
                trailing: item.allowsHeatLevelSelection
                    ? ElevatedButton(
                        onPressed: _canAddMore(category)
                            ? () => _addItem(category, item)
                            : null,
                        child: const Text('Add'),
                      )
                    : Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.remove_circle_outline),
                            onPressed: itemCount > 0
                                ? () => _removeItem(category, item)
                                : null,
                          ),
                          Text('$itemCount'),
                          IconButton(
                            icon: const Icon(Icons.add_circle_outline),
                            onPressed: _canAddMore(category)
                                ? () => _addItem(category, item)
                                : null,
                          ),
                        ],
                      ),
              );
            }).toList(),
        ] else if (category == 'Sides') ...[
          ...(_categoryItems[category] ?? []).map((item) {
            final itemCount = (_selectedItems[category] ?? []).where((selected) {
              if (selected is MenuItem) {
                return selected.id == item.id;
              } else if (selected is Map && selected['item'] is MenuItem) {
                return (selected['item'] as MenuItem).id == item.id;
              }
              return false;
            }).length;
            return ListTile(
              title: Text(item.name),
              subtitle: Text('Selected: $itemCount'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.remove_circle_outline),
                    onPressed: itemCount > 0
                        ? () => _removeItem(category, item)
                        : null,
                  ),
                  Text('$itemCount'),
                  IconButton(
                    icon: const Icon(Icons.add_circle_outline),
                    onPressed: _canAddMore(category)
                        ? () => _addItem(category, item)
                        : null,
                  ),
                ],
              ),
            );
          }).toList(),
        ] else ...[
          // Generic handling for other categories
          ...(_categoryItems[category] ?? []).map((item) {
            final itemCount = (_selectedItems[category] ?? []).where((selected) {
              if (selected is MenuItem) {
                return selected.id == item.id;
              } else if (selected is Map && selected['item'] is MenuItem) {
                return (selected['item'] as MenuItem).id == item.id;
              }
              return false;
            }).length;
            return ListTile(
              title: Text(item.name),
              subtitle: Text('Selected: $itemCount'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.remove_circle_outline),
                    onPressed: itemCount > 0
                        ? () => _removeItem(category, item)
                        : null,
                  ),
                  Text('$itemCount'),
                  IconButton(
                    icon: const Icon(Icons.add_circle_outline),
                    onPressed: _canAddMore(category)
                        ? () => _addItem(category, item)
                        : null,
                  ),
                ],
              ),
            );
          }).toList(),
        ],
        const Divider(height: 32),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.crewPack.name),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.crewPack.description,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 24),
              Text(
                'Please make your selections for each component of the pack below.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),

              // Add sauce selection button if applicable
              if (widget.crewPack.allowsSauceSelection) ...[
                ElevatedButton.icon(
                  onPressed: _handleSauceSelection,
                  icon: const Icon(Icons.local_dining),
                  label: Text(
                    widget.crewPack.selectedSauces?.isEmpty ?? true
                        ? 'Select ${widget.crewPack.includedSauceCount} Sauces'
                        : 'Selected Sauces: ${widget.crewPack.selectedSauces!.join(", ")}',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepOrange,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Existing category selections
              ...widget.crewPack.customizationCategories?.map(_buildCategorySection) ?? [],

              const SizedBox(height: 24),
              Center(
                child: ElevatedButton(
                  onPressed: () {
                    final validationMessage = _getValidationMessage();
                    if (validationMessage != null) {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: const Text('Incomplete Selection'),
                            content: Text(validationMessage),
                            actions: <Widget>[
                              TextButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                child: const Text('OK'),
                              ),
                            ],
                          );
                        },
                      );
                    } else {
                      // Pass all customization details back to the cart.
                      // The `_selectedItems` map contains items with heat levels,
                      // and `_crewPackSelections` contains sandwich customizations.
                      // This data is passed back to the calling screen, which is
                      // responsible for adding it to the cart.
                      Navigator.pop(context, {
                        'sandwiches': _crewPackSelections['Sandwiches'],
                        'customizations': _selectedItems,
                        'sauces': widget.crewPack.selectedSauces,
                      });
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepOrange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  ),
                  child: const Text('Add to Cart'),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: CustomBottomNavBar(
        selectedIndex: 2, // Menu section
        onItemSelected: (index) {
          if (index != 2) { // If not menu
            Navigator.pop(context); // Return to main layout
          }
        },
      ),
    );
  }
}
