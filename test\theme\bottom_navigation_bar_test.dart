import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:qsr_app/themes/app_theme.dart';

void main() {
  testWidgets('BottomNavigationBar dark theme is applied correctly', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        theme: AppTheme.darkTheme,
        home: Scaffold(
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: 0, // Home is selected
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.menu),
                label: 'Menu',
              ),
            ],
          ),
        ),
      ),
    );

    final bottomNavBarFinder = find.byType(BottomNavigationBar);

    // Verify the background color
    final Material material = tester.firstWidget(find.descendant(
      of: bottomNavBarFinder,
      matching: find.byType(Material),
    ));
    expect(material.color, AppTheme.darkTheme.bottomNavigationBarTheme.backgroundColor);

    // Verify selected item colors
    final selectedIconElement = tester.element(find.byIcon(Icons.home));
    final selectedIconTheme = IconTheme.of(selectedIconElement);
    expect(selectedIconTheme.color, AppTheme.darkTheme.bottomNavigationBarTheme.selectedItemColor);

    final selectedLabelElement = tester.element(find.text('Home'));
    final selectedTextStyle = DefaultTextStyle.of(selectedLabelElement);
    expect(selectedTextStyle.style.color, AppTheme.darkTheme.bottomNavigationBarTheme.selectedItemColor);

    // Verify unselected item colors
    final unselectedIconElement = tester.element(find.byIcon(Icons.menu));
    final unselectedIconTheme = IconTheme.of(unselectedIconElement);
    expect(unselectedIconTheme.color, AppTheme.darkTheme.bottomNavigationBarTheme.unselectedItemColor);

    final unselectedLabelElement = tester.element(find.text('Menu'));
    final unselectedTextStyle = DefaultTextStyle.of(unselectedLabelElement);
    expect(unselectedTextStyle.style.color, AppTheme.darkTheme.bottomNavigationBarTheme.unselectedItemColor);
  });
}
