import 'package:flutter/material.dart';

class AppTypography {
  // Display Text - Sofia Rough Black Three
  static const displayLarge = TextStyle(
    fontFamily: 'SofiaRoughBlackThree',
    fontSize: 57,
    height: 1.12,
    letterSpacing: -0.25,
  );

  static const displayMedium = TextStyle(
    fontFamily: 'SofiaRoughBlackThree',
    fontSize: 45,
    height: 1.15,
  );

  static const displaySmall = TextStyle(
    fontFamily: 'SofiaRoughBlackThree',
    fontSize: 36,
    height: 1.22,
  );

  // Headlines - Montserrat Black
  static const headlineLarge = TextStyle(
    fontFamily: 'MontserratBlack',
    fontSize: 32,
    height: 1.25,
  );

  static const headlineMedium = TextStyle(
    fontFamily: 'MontserratBlack',
    fontSize: 28,
    height: 1.28,
  );

  static const headlineSmall = TextStyle(
    fontFamily: 'MontserratBlack',
    fontSize: 24,
    height: 1.33,
  );

  // Body Text - Sofia Sans
  static const bodyLarge = TextStyle(
    fontFamily: 'SofiaSans',
    fontSize: 16,
    height: 1.5,
    letterSpacing: 0.15,
    fontWeight: FontWeight.w400,
  );

  static const bodyMedium = TextStyle(
    fontFamily: 'SofiaSans',
    fontSize: 14,
    height: 1.42,
    letterSpacing: 0.25,
    fontWeight: FontWeight.w400,
  );

  static const bodySmall = TextStyle(
    fontFamily: 'SofiaSans',
    fontSize: 12,
    height: 1.33,
    letterSpacing: 0.4,
    fontWeight: FontWeight.w400,
  );

  // Navigation - Montserrat Black
  static const navigation = TextStyle(
    fontFamily: 'MontserratBlack',
    fontSize: 12,
    height: 1.33,
    letterSpacing: 0.4,
  );

  // Button Text - Montserrat Black
  static const button = TextStyle(
    fontFamily: 'MontserratBlack',
    fontSize: 14,
    height: 1.42,
    letterSpacing: 0.1,
  );

  // Title Text - Sofia Sans
  static const titleLarge = TextStyle(
    fontFamily: 'SofiaSans',
    fontSize: 22,
    height: 1.27,
    fontWeight: FontWeight.w600,
  );

  static const titleMedium = TextStyle(
    fontFamily: 'SofiaSans',
    fontSize: 16,
    height: 1.5,
    letterSpacing: 0.15,
    fontWeight: FontWeight.w500,
  );

  static const titleSmall = TextStyle(
    fontFamily: 'SofiaSans',
    fontSize: 14,
    height: 1.42,
    letterSpacing: 0.1,
    fontWeight: FontWeight.w500,
  );

  // Label Text - Sofia Sans
  static const labelLarge = TextStyle(
    fontFamily: 'SofiaSans',
    fontSize: 14,
    height: 1.42,
    letterSpacing: 0.1,
    fontWeight: FontWeight.w500,
  );

  static const labelMedium = TextStyle(
    fontFamily: 'SofiaSans',
    fontSize: 12,
    height: 1.33,
    letterSpacing: 0.5,
    fontWeight: FontWeight.w500,
  );

  static const labelSmall = TextStyle(
    fontFamily: 'SofiaSans',
    fontSize: 11,
    height: 1.45,
    letterSpacing: 0.5,
    fontWeight: FontWeight.w500,
  );
}
