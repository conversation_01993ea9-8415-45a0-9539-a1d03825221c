{
  "name": "CHICA'S Chicken Accessibility Configuration",
  "description": "VS Code settings optimized for accessibility development and testing",
  
  "settings": {
    // Accessibility-focused editor settings
    "editor.accessibilitySupport": "on",
    "editor.accessibilityPageSize": 10,
    "editor.cursorBlinking": "solid",
    "editor.cursorStyle": "block",
    "editor.fontSize": 14,
    "editor.fontWeight": "normal",
    "editor.lineHeight": 1.6,
    "editor.wordWrap": "on",
    "editor.rulers": [80, 120],
    
    // High contrast theme support
    "workbench.colorTheme": "Default High Contrast",
    "workbench.preferredHighContrastColorTheme": "Default High Contrast",
    
    // Screen reader optimizations
    "editor.screenReaderAnnounceInlineSuggestions": true,
    "diffEditor.ignoreTrimWhitespace": false,
    "editor.guides.bracketPairs": true,
    "editor.guides.highlightActiveIndentation": true,
    
    // Flutter-specific accessibility settings
    "dart.flutterOutline": true,
    "dart.closingLabels": true,
    "dart.previewFlutterUiGuides": true,
    "dart.previewFlutterUiGuidesCustomTracking": true,
    
    // Testing configuration
    "dart.flutterTestAdditionalArgs": [
      "--accessibility-testing"
    ],
    
    // Linting for accessibility
    "dart.lineLength": 120,
    "dart.enableSdkFormatter": true,
    "editor.formatOnSave": true,
    
    // Terminal accessibility
    "terminal.integrated.fontSize": 14,
    "terminal.integrated.lineHeight": 1.4,
    "terminal.integrated.accessibilitySupport": "on"
  },
  
  "extensions": {
    "recommendations": [
      "dart-code.dart-code",
      "dart-code.flutter",
      "ms-vscode.vscode-json",
      "bradlc.vscode-tailwindcss",
      "streetsidesoftware.code-spell-checker",
      "ms-vscode.accessibility-insights"
    ]
  },
  
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Run Accessibility Tests",
        "type": "shell",
        "command": "flutter",
        "args": [
          "test",
          "test/accessibility/accessibility_test_suite.dart",
          "--reporter=expanded"
        ],
        "group": {
          "kind": "test",
          "isDefault": true
        },
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        },
        "problemMatcher": "$dart-flutter-test"
      },
      {
        "label": "Flutter Accessibility Inspector",
        "type": "shell",
        "command": "flutter",
        "args": [
          "run",
          "--debug",
          "--enable-software-rendering"
        ],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        }
      },
      {
        "label": "Generate Accessibility Report",
        "type": "shell",
        "command": "dart",
        "args": [
          "run",
          "test/accessibility/generate_report.dart"
        ],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        }
      },
      {
        "label": "Color Contrast Audit",
        "type": "shell",
        "command": "dart",
        "args": [
          "run",
          "test/accessibility/color_contrast_audit.dart"
        ],
        "group": "test",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        }
      }
    ]
  },
  
  "launch": {
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Flutter (Accessibility Debug)",
        "request": "launch",
        "type": "dart",
        "program": "lib/main.dart",
        "args": [
          "--enable-software-rendering",
          "--accessibility-testing"
        ],
        "flutterMode": "debug",
        "env": {
          "FLUTTER_ACCESSIBILITY_TESTING": "true"
        }
      },
      {
        "name": "Flutter (High Contrast Mode)",
        "request": "launch",
        "type": "dart",
        "program": "lib/main.dart",
        "args": [
          "--enable-software-rendering"
        ],
        "flutterMode": "debug",
        "env": {
          "FLUTTER_HIGH_CONTRAST": "true"
        }
      },
      {
        "name": "Integration Tests (Accessibility)",
        "request": "launch",
        "type": "dart",
        "program": "integration_test/accessibility_integration_test.dart",
        "flutterMode": "debug"
      }
    ]
  },
  
  "snippets": {
    "dart": {
      "Accessible Widget": {
        "prefix": "accessible-widget",
        "body": [
          "Semantics(",
          "  label: '${1:Semantic label}',",
          "  hint: '${2:Hint for screen readers}',",
          "  button: ${3:true},",
          "  enabled: ${4:true},",
          "  child: ${5:Widget},",
          ")"
        ],
        "description": "Create an accessible widget with proper semantics"
      },
      "WCAG Button": {
        "prefix": "wcag-button",
        "body": [
          "WCAGButton(",
          "  onPressed: ${1:() {}},",
          "  semanticLabel: '${2:Button description}',",
          "  tooltip: '${3:Button tooltip}',",
          "  child: Text('${4:Button text}'),",
          ")"
        ],
        "description": "Create a WCAG compliant button"
      },
      "Accessible Form Field": {
        "prefix": "accessible-form",
        "body": [
          "AccessibleFormField(",
          "  label: '${1:Field label}',",
          "  hint: '${2:Field hint}',",
          "  required: ${3:true},",
          "  controller: ${4:controller},",
          "  validator: ${5:(value) => null},",
          ")"
        ],
        "description": "Create an accessible form field"
      },
      "Color Contrast Check": {
        "prefix": "contrast-check",
        "body": [
          "ColorContrastValidator.meetsWCAGAA(",
          "  ${1:foregroundColor},",
          "  ${2:backgroundColor},",
          "  isLargeText: ${3:false},",
          ")"
        ],
        "description": "Check color contrast compliance"
      }
    }
  },
  
  "keybindings": [
    {
      "key": "ctrl+shift+a",
      "command": "workbench.action.tasks.runTask",
      "args": "Run Accessibility Tests"
    },
    {
      "key": "ctrl+shift+c",
      "command": "workbench.action.tasks.runTask",
      "args": "Color Contrast Audit"
    },
    {
      "key": "ctrl+shift+r",
      "command": "workbench.action.tasks.runTask",
      "args": "Generate Accessibility Report"
    }
  ],
  
  "accessibility_checklist": {
    "wcag_2_1_aa_requirements": [
      {
        "criterion": "1.1.1",
        "name": "Non-text Content",
        "description": "All images have appropriate alt text",
        "test_method": "Check all Image widgets have semanticLabel"
      },
      {
        "criterion": "1.3.1",
        "name": "Info and Relationships",
        "description": "Semantic structure is properly conveyed",
        "test_method": "Verify proper heading hierarchy and semantic markup"
      },
      {
        "criterion": "1.4.3",
        "name": "Contrast (Minimum)",
        "description": "Text has contrast ratio of at least 4.5:1",
        "test_method": "Run color contrast audit"
      },
      {
        "criterion": "1.4.4",
        "name": "Resize text",
        "description": "Text can be resized up to 200% without loss of functionality",
        "test_method": "Test with textScaleFactor up to 2.0"
      },
      {
        "criterion": "2.1.1",
        "name": "Keyboard",
        "description": "All functionality available via keyboard",
        "test_method": "Test tab navigation and keyboard shortcuts"
      },
      {
        "criterion": "2.4.3",
        "name": "Focus Order",
        "description": "Focus order is logical and intuitive",
        "test_method": "Test tab order through all interactive elements"
      },
      {
        "criterion": "2.4.7",
        "name": "Focus Visible",
        "description": "Focus indicators are clearly visible",
        "test_method": "Verify focus indicators on all interactive elements"
      },
      {
        "criterion": "2.5.5",
        "name": "Target Size",
        "description": "Touch targets are at least 44x44 CSS pixels",
        "test_method": "Measure all interactive element sizes"
      },
      {
        "criterion": "3.2.2",
        "name": "On Input",
        "description": "Input doesn't cause unexpected context changes",
        "test_method": "Test form interactions don't cause navigation"
      },
      {
        "criterion": "3.3.1",
        "name": "Error Identification",
        "description": "Errors are clearly identified and described",
        "test_method": "Test form validation and error messages"
      },
      {
        "criterion": "3.3.2",
        "name": "Labels or Instructions",
        "description": "Form fields have clear labels",
        "test_method": "Verify all form fields have proper labels"
      },
      {
        "criterion": "4.1.2",
        "name": "Name, Role, Value",
        "description": "UI components have accessible names and roles",
        "test_method": "Test with screen reader and semantic analysis"
      }
    ]
  }
}
