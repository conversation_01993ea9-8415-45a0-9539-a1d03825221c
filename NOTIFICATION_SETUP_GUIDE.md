# 🔔 <PERSON>ca's Chicken - Notification Setup Guide

## 📋 What We Built

You now have a complete notification system that:
- ✅ Sends daily reminders at 6 PM to encourage feedback
- ✅ Uses deep linking to navigate directly to the loyalty page
- ✅ Works with both local notifications (for testing) and Firebase Cloud Messaging
- ✅ Includes user settings to enable/disable notifications
- ✅ Has platform-specific configurations for Android and iOS

## 🚀 Quick Start Testing

### 1. Test Local Notifications (Works Immediately)
```dart
// Add this to any screen to test notifications
import '../widgets/notification_settings_button.dart';

// In your widget build method:
TestNotificationButton(
  onPressed: () async {
    final notificationService = NotificationService();
    await notificationService.sendTestNotification();
  },
)
```

### 2. Navigate to Notification Settings
```dart
// Add this button anywhere in your app
NotificationSettingsButton(showAsCard: true)

// Or navigate programmatically
Navigator.of(context).pushNamed('/notification-settings');
```

### 3. Test Deep Linking
- Send a test notification
- Tap the notification when it appears
- The app should open and navigate to the loyalty page

## 🔧 Firebase Setup (For Production Push Notifications)

### Step 1: Firebase Console Setup
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your "Chica's Chicken" project
3. Go to Project Settings → Cloud Messaging
4. Note your Server Key (for backend integration)

### Step 2: Android Setup
1. Download `google-services.json` from Firebase Console
2. Place it in `android/app/google-services.json`
3. The AndroidManifest.xml is already configured!

### Step 3: iOS Setup
1. Download `GoogleService-Info.plist` from Firebase Console
2. Add it to `ios/Runner/GoogleService-Info.plist`
3. The Info.plist is already configured!

## 📱 Platform-Specific Features

### Android Features
- ✅ Notification channels configured
- ✅ Deep linking with `chicaschicken://` scheme
- ✅ Background notification handling
- ✅ Notification permissions

### iOS Features
- ✅ Deep linking with URL schemes
- ✅ Background notification modes
- ✅ Notification permissions
- ✅ Rich notifications support

## 🧪 Testing Checklist

### Local Testing (No Firebase Required)
- [ ] Run the app
- [ ] Go to notification settings (`/notification-settings`)
- [ ] Send a test notification
- [ ] Verify notification appears
- [ ] Tap notification and verify it opens loyalty page

### Firebase Testing (Requires Setup)
- [ ] Complete Firebase setup above
- [ ] Run app on physical device
- [ ] Enable notifications in settings
- [ ] Wait for 6 PM or change time in code for testing
- [ ] Verify daily notification appears

### Deep Linking Testing
- [ ] Send notification
- [ ] Tap notification from notification panel
- [ ] Verify app opens to loyalty page
- [ ] Test with app closed, backgrounded, and foreground

## 🎯 How to Use in Your App

### Add to Settings Screen
```dart
// In your settings or profile screen
NotificationSettingsButton(
  showAsCard: true,
  customText: "Notification Preferences",
)
```

### Add Quick Toggle
```dart
// For a simple on/off switch
QuickNotificationToggle(
  label: "Daily Reminders",
  onChanged: (enabled) {
    print("Notifications ${enabled ? 'enabled' : 'disabled'}");
  },
)
```

### Schedule Notifications Programmatically
```dart
// In your app initialization or user login
final notificationService = NotificationService();
await notificationService.initialize();
await notificationService.scheduleDailyFeedbackNotification();
```

## 🔧 Customization Options

### Change Notification Time
In `notification_service.dart`, line ~280:
```dart
// Change from 6 PM to any time you want
DateTime scheduledTime = DateTime(now.year, now.month, now.day, 18, 0); // 18 = 6 PM
```

### Customize Notification Message
In `notification_service.dart`, line ~300:
```dart
await _localNotifications.zonedSchedule(
  1,
  '🌟 Your Custom Title!', // Change this
  'Your custom message here!', // Change this
  // ... rest of the code
);
```

### Change Deep Link Destination
In `notification_service.dart`, line ~305:
```dart
payload: '/your-custom-route', // Change from '/loyalty' to any route
```

## 🐛 Troubleshooting

### Notifications Not Appearing
1. Check device notification settings
2. Verify app has notification permissions
3. Test with `sendTestNotification()` first
4. Check device Do Not Disturb settings

### Deep Linking Not Working
1. Verify AndroidManifest.xml has intent filters
2. Check iOS Info.plist has URL schemes
3. Test with app in different states (closed, background, foreground)
4. Verify route exists in main.dart

### Firebase Issues
1. Verify `google-services.json` and `GoogleService-Info.plist` are in correct locations
2. Check Firebase project configuration
3. Ensure app bundle ID matches Firebase project
4. Test on physical device (not simulator for push notifications)

## 📚 Next Steps

1. **Test Everything**: Use the testing checklist above
2. **Customize Messages**: Update notification text to match your brand
3. **Add Analytics**: Track notification engagement
4. **A/B Testing**: Test different notification times and messages
5. **User Feedback**: Ask users about notification preferences

## 🎉 You're Done!

Your notification system is now ready! Users will receive friendly daily reminders at 6 PM to share feedback and earn rewards. The notifications will take them directly to your loyalty page where they can engage with your rewards program.

Remember: Always test on real devices for the best experience, especially for push notifications and deep linking!
