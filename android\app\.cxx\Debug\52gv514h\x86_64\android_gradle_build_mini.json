{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Bizness\\7thSenseMediaLabz\\Chi<PERSON>'s Chicken Flutter\\android\\app\\.cxx\\Debug\\52gv514h\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Bizness\\7thSenseMediaLabz\\Chi<PERSON>'s Chicken Flutter\\android\\app\\.cxx\\Debug\\52gv514h\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}