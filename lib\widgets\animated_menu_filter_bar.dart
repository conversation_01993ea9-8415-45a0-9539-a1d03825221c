import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/animation_utils.dart';

class AnimatedMenuFilterBar extends StatefulWidget {
  final List<String> categories;
  final String selectedCategory;
  final Function(String) onCategorySelected;

  const AnimatedMenuFilterBar({
    Key? key,
    required this.categories,
    required this.selectedCategory,
    required this.onCategorySelected,
  }) : super(key: key);

  @override
  _AnimatedMenuFilterBarState createState() => _AnimatedMenuFilterBarState();
}

class _AnimatedMenuFilterBarState extends State<AnimatedMenuFilterBar> {
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.categories.length,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemBuilder: (context, index) {
          final category = widget.categories[index];
          final isSelected = category == widget.selectedCategory;
          
          return Padding(
            padding: const EdgeInsets.only(right: 10),
            child: MouseRegion(
              onEnter: (_) => setState(() => _isHovering = true),
              onExit: (_) => setState(() => _isHovering = false),
              child: Material(
                color: Colors.transparent,
                elevation: 0, // Ensure no default shadow
                shadowColor: Colors.transparent, // Ensure no default shadow color
                shape: const Border(), // Explicitly set shape to remove default border
                child: InkWell(
                  onTap: () => widget.onCategorySelected(category),
                  splashColor: Colors.transparent, // Remove splash effect
                  highlightColor: Colors.transparent, // Remove highlight effect
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Center(
                      child: Text(
                        category,
                        style: TextStyle(
                          color: isSelected || _isHovering ? Colors.green : Colors.black,
                          fontWeight: FontWeight.bold,
                          decoration: isSelected || _isHovering ? TextDecoration.underline : TextDecoration.none,
                          decorationColor: Colors.green,
                          decorationThickness: 2,
                        ),
                      ),
                    ),
                  ).animate(target: isSelected ? 1 : 0)
                    .scale(
                      duration: AnimationDurations.fast,
                      begin: const Offset(1, 1),
                      end: const Offset(1.05, 1.05),
                      curve: GSAPCurves.power2InOut,
                    ),
                ),
              ),
            ),
          ).animate()
            .fadeIn(
              delay: Duration(milliseconds: (index * 100)),
              duration: AnimationDurations.normal,
            )
            .slideX(
              begin: 0.25,
              end: 0,
              delay: Duration(milliseconds: (index * 100)),
              duration: AnimationDurations.normal,
              curve: GSAPCurves.backOut,
            );
        },
      ),
    );
  }
}
