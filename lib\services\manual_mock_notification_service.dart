import 'dart:async';
import 'package:qsr_app/services/notification_service.dart';

class MockNotificationService implements NotificationService {
  @override
  Future<void> initialize({Function(String)? onNotificationTap}) async {}

  @override
  Future<void> showLocalNotification({required String title, required String body, String? payload}) async {}

  @override
  Future<void> scheduleDailyFeedbackNotification() async {}

  @override
  Future<void> sendTestNotification() async {}

  @override
  Future<bool> getDailyFeedbackEnabled() async => true;

  @override
  Future<void> setDailyFeedbackEnabled(bool enabled) async {}

  @override
  Future<bool> areNotificationsEnabled() async => true;

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {}

  @override
  Stream<Map<String, dynamic>> get notificationStream => const Stream.empty();

  @override
  Stream<Map<String, dynamic>> get orderUpdateStream => const Stream.empty();

  @override
  bool get isWebSocketConnected => false;

  @override
  void dispose() {}

  @override
  Future<String?> getInitialLink() async => null;

  @override
  Stream<String?> get onLinkStream => const Stream.empty();
}
