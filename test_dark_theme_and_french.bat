@echo off
echo 🌙🇫🇷 TESTING DARK THEME AND FRENCH LANGUAGE
echo.
echo This will launch the app to test both completed features:
echo 1. Dark Theme for Menu Item Categories
echo 2. French Language Implementation
echo.

echo 📦 Getting dependencies...
flutter pub get
echo.

echo 🌐 Launching on Chrome...
echo.
echo 🧪 TESTING CHECKLIST:
echo.
echo ✅ DARK THEME TEST:
echo    1. Open hamburger menu (top-right)
echo    2. Switch to Dark Mode
echo    3. Go to Menu → Chicken Bites (or any category)
echo    4. Verify: Dark teal backgrounds, red accents, white text
echo.
echo ✅ FRENCH LANGUAGE TEST:
echo    1. Open hamburger menu (top-right)
echo    2. Click "Français" button
echo    3. Verify navigation: HOME → ACCUEIL, GAMES → JEUX
echo    4. Verify home screen: "TASTE CHICA'S" → "GOÛTEZ CHICA'S"
echo    5. Go to menu and verify: "Add to Cart" → "Ajouter au panier"
echo.
echo 🎯 COMBINED TEST:
echo    1. Switch to French + Dark Mode
echo    2. Navigate through menu categories
echo    3. Verify both features work together perfectly
echo.

flutter run -d chrome --web-renderer html
