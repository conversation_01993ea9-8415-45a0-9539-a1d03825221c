# Plan for Styling Changes in menu_screen.dart

This plan outlines the steps to modify the styling of text elements and bullet points in the `lib/screens/menu_screen.dart` file.

**Objective:**

Implement the following styling changes:
1.  Make the subtitle text around line 146 left-aligned.
2.  Decrease the vertical spacing between the bullet point text content lines.
3.  Apply the Montserrat Black 900 font to the title text "Craving Crunch? Make Your Cheat Day Count!".

**Detailed Steps:**

1.  **Modify the text alignment for the subtitle text:**
    *   Locate the `Text` widget around line 146 (which contains the text 'Indulge in our irresistible crispy, juicy, and perfectly spiced hot chicken. Enjoy the experience - it\'s about quality, not just quantity').
    *   Change the `textAlign` property from `TextAlign.center` to `TextAlign.left`.

2.  **Adjust the spacing between bullet points:**
    *   Locate the `SmartOrderingTip` widget definition within `lib/screens/menu_screen.dart` (around line 521).
    *   Find the `Padding` widget wrapping the `Row` in the `build` method.
    *   Change `const EdgeInsets.symmetric(vertical: 4.0)` to `const EdgeInsets.symmetric(vertical: 2.0)`.

3.  **Apply the Montserrat Black 900 font to the title text:**
    *   Locate the `Text` widget on line 127 (which contains the text 'CRAVING CRUNCH? MAKE YOUR CHEAT DAY COUNT!').
    *   Modify the `style` property's `TextStyle`.
    *   Add or update the `fontFamily` property to `'MontserratBlack'`.
    *   Ensure the `fontWeight` is set to `FontWeight.w900`.

**Implementation Mode:**

The implementation of this plan will require switching to the `code` mode to modify the `lib/screens/menu_screen.dart` file.