<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="geminiGrad512" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    <filter id="glow512">
      <feGaussianBlur stdDeviation="25.6" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with gradient -->
  <rect x="0" y="0" width="512" height="512" rx="96" fill="url(#geminiGrad512)"/>
  
  <!-- Gemini constellation symbol -->
  <g transform="translate(256,256)" filter="url(#glow512)">
    <!-- Top star shape -->
    <path d="M0,-160 L40,0 L-40,0 Z" fill="white" opacity="0.95"/>
    <!-- Bottom star shape -->
    <path d="M0,160 L-40,0 L40,0 Z" fill="white" opacity="0.85"/>
    <!-- Connecting elements -->
    <circle cx="0" cy="-80" r="24" fill="white" opacity="0.9"/>
    <circle cx="0" cy="80" r="24" fill="white" opacity="0.9"/>
    <line x1="0" y1="-80" x2="0" y2="80" stroke="white" stroke-width="16" opacity="0.7"/>
  </g>
</svg>