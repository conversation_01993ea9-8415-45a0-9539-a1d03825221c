@echo off
echo 🌙 TESTING DARK THEME UPDATE
echo.
echo This will launch the app to test the new Rebel Rooster inspired dark theme
echo.

echo 📦 Getting dependencies...
flutter pub get
echo.

echo 🌐 Launching on Chrome...
echo The app will open in your default browser
echo.
echo 💡 To test dark theme:
echo 1. Open the app in Chrome
echo 2. Go to Settings or look for theme toggle
echo 3. Switch to Dark Mode
echo 4. Check the new dark teal color scheme
echo.

flutter run -d chrome --web-renderer html
