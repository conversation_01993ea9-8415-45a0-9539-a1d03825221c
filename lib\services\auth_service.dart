import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../models/user_model.dart';
import '../models/auth_state.dart';

/// 🔐 Mock Authentication Service for CHICA'S Chicken
/// This service provides dummy authentication operations for development/testing
/// when Firebase and other external auth providers are not enabled.
class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // Secure storage for sensitive data (still useful for mock scenarios)
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // State management
  AuthState _authState = AuthState.initial();
  UserModel? _currentUser;
  // StreamSubscription<User?>? _authStateSubscription; // No Firebase User
  Timer? _sessionTimer;

  // Configuration (simplified for mock)
  static const Duration _sessionTimeout = Duration(minutes: 30);
  static const int _maxLoginAttempts = 5;
  static const Duration _lockoutDuration = Duration(minutes: 15);

  // Getters
  AuthState get authState => _authState;
  UserModel? get currentUser => _currentUser;
  bool get isAuthenticated => _authState.isAuthenticated;
  // User? get firebaseUser => null; // No Firebase User

  /// Initialize the authentication service
  Future<void> initialize() async {
    try {
      _setAuthState(AuthState.loading());
      
      // Simulate a delay for initialization
      await Future.delayed(const Duration(milliseconds: 500));

      // In a mock service, we might simulate an existing session or start unauthenticated
      // For now, we'll start unauthenticated.
      _setAuthState(AuthState.unauthenticated());
      
    } catch (e) {
      _setAuthState(AuthState.error(message: 'Failed to initialize mock authentication: $e'));
    }
  }

  /// Handle mock auth state changes (simplified)
  // Future<void> _onAuthStateChanged(User? user) async { // No Firebase User
  //   if (user != null) {
  //     // Simulate user loading
  //     _currentUser = UserModel(
  //       uid: user.uid,
  //       email: user.email!,
  //       name: user.displayName ?? 'Mock User',
  //       emailVerified: user.emailVerified,
  //       addresses: [],
  //       preferences: const UserPreferences(
  //         favoriteItems: [],
  //         dietaryRestrictions: [],
  //         pushNotifications: true,
  //         emailNotifications: true,
  //         smsNotifications: false,
  //         preferredLanguage: 'en',
  //         biometricAuth: false,
  //         theme: 'system',
  //       ),
  //       loyaltyInfo: const LoyaltyInfo(
  //         points: 0,
  //         tier: 'Bronze',
  //         totalSpent: 0,
  //         totalOrders: 0,
  //         availableRewards: [],
  //       ),
  //       createdAt: DateTime.now(),
  //       lastLogin: DateTime.now(),
  //     );
  //     _setAuthState(AuthState.authenticated(
  //       userId: user.uid,
  //       email: user.email!,
  //       displayName: user.displayName,
  //       photoUrl: user.photoURL,
  //       emailVerified: user.emailVerified,
  //     ));
  //     _startSessionTimer();
  //   } else {
  //     _currentUser = null;
  //     _setAuthState(AuthState.unauthenticated());
  //     _stopSessionTimer();
  //   }
  // }

  /// Check for existing session (mocked)
  Future<void> _checkExistingSession() async {
    // In a mock service, we might not persist sessions or have a simpler check
    // For now, we'll just assume no existing session on startup.
    _setAuthState(AuthState.unauthenticated());
  }

  /// Sign up with email and password (mocked)
  Future<AuthState> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    String? phone,
  }) async {
    _setAuthState(AuthState.loading());
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

    if (email == '<EMAIL>') {
      final error = AuthState.error(
        message: 'An account with this email already exists (mock error)',
        code: AuthErrorCodes.emailAlreadyInUse,
      );
      _setAuthState(error);
      return error;
    }

    // Simulate successful signup
    _currentUser = UserModel(
      uid: 'mock_user_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      name: name,
      phone: phone,
      emailVerified: false,
      addresses: [],
      preferences: const UserPreferences(
        favoriteItems: [],
        dietaryRestrictions: [],
        pushNotifications: true,
        emailNotifications: true,
        smsNotifications: false,
        preferredLanguage: 'en',
        biometricAuth: false,
        theme: 'system',
      ),
      loyaltyInfo: const LoyaltyInfo(
        points: 100, // Welcome bonus
        tier: 'Bronze',
        totalSpent: 0,
        totalOrders: 0,
        availableRewards: [],
      ),
      createdAt: DateTime.now(),
      lastLogin: DateTime.now(),
    );

    _setAuthState(AuthState.authenticated(
      userId: _currentUser!.uid,
      email: _currentUser!.email,
      displayName: _currentUser!.name,
      emailVerified: _currentUser!.emailVerified,
    ));
    return _authState;
  }

  /// Sign in with email and password (mocked)
  Future<AuthState> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    _setAuthState(AuthState.loading());
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

    if (email == '<EMAIL>') {
      final error = AuthState.error(
        message: 'Mock sign-in error: Invalid credentials',
        code: AuthErrorCodes.wrongPassword,
      );
      _setAuthState(error);
      return error;
    }

    // Simulate successful sign-in
    _currentUser = UserModel(
      uid: 'mock_user_123',
      email: email,
      name: 'Mock User',
      emailVerified: true,
      addresses: [],
      preferences: const UserPreferences(
        favoriteItems: [],
        dietaryRestrictions: [],
        pushNotifications: true,
        emailNotifications: true,
        smsNotifications: false,
        preferredLanguage: 'en',
        biometricAuth: false,
        theme: 'system',
      ),
      loyaltyInfo: const LoyaltyInfo(
        points: 500,
        tier: 'Gold',
        totalSpent: 10000,
        totalOrders: 10,
        availableRewards: [],
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      lastLogin: DateTime.now(),
    );

    _setAuthState(AuthState.authenticated(
      userId: _currentUser!.uid,
      email: _currentUser!.email,
      displayName: _currentUser!.name,
      emailVerified: _currentUser!.emailVerified,
    ));
    return _authState;
  }

  /// Sign in with Google (mocked)
  Future<AuthState> signInWithGoogle() async {
    _setAuthState(AuthState.loading());
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

    // Simulate successful Google sign-in
    _currentUser = UserModel(
      uid: 'mock_google_user_456',
      email: '<EMAIL>',
      name: 'Mock Google User',
      profileImageUrl: 'https://example.com/mock_profile.jpg',
      emailVerified: true,
      addresses: [],
      preferences: const UserPreferences(
        favoriteItems: [],
        dietaryRestrictions: [],
        pushNotifications: true,
        emailNotifications: true,
        smsNotifications: false,
        preferredLanguage: 'en',
        biometricAuth: false,
        theme: 'system',
      ),
      loyaltyInfo: const LoyaltyInfo(
        points: 200,
        tier: 'Silver',
        totalSpent: 5000,
        totalOrders: 5,
        availableRewards: [],
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      lastLogin: DateTime.now(),
    );

    _setAuthState(AuthState.authenticated(
      userId: _currentUser!.uid,
      email: _currentUser!.email,
      displayName: _currentUser!.name,
      photoUrl: _currentUser!.profileImageUrl,
      emailVerified: _currentUser!.emailVerified,
    ));
    return _authState;
  }

  /// Sign out (mocked)
  Future<void> signOut() async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate delay
    _currentUser = null;
    _setAuthState(AuthState.unauthenticated());
    _stopSessionTimer();
  }

  /// Send password reset email (mocked)
  Future<bool> sendPasswordResetEmail(String email) async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate delay
    debugPrint('Mock: Sent password reset email to $email');
    return true;
  }

  /// Update user profile (mocked)
  Future<bool> updateUserProfile(UserModel updatedUser) async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate delay
    _currentUser = updatedUser;
    notifyListeners();
    debugPrint('Mock: User profile updated for ${updatedUser.email}');
    return true;
  }

  /// Check if biometric authentication is available (mocked)
  Future<bool> isBiometricAvailable() async {
    // Always return false in mock if local_auth is not enabled
    return false;
  }

  /// Authenticate with biometrics (mocked)
  Future<BiometricAuthResult> authenticateWithBiometrics() async {
    // Always return failure in mock if local_auth is not enabled
    return BiometricAuthResult.failure(
      message: 'Biometric authentication is not available in this build configuration.',
      errorType: BiometricAuthError.notAvailable,
    );
  }

  /// Private helper methods
  void _setAuthState(AuthState newState) {
    _authState = newState;
    notifyListeners();
  }

  // Mock error handling (no FirebaseAuthException)
  // AuthState _handleFirebaseAuthError(FirebaseAuthException e) {
  //   return AuthState.error(message: 'Mock Auth Error: ${e.message}', code: e.code);
  // }

  // Mock user profile creation (no Firestore)
  // Future<void> _createUserProfile(User user) async {
  //   debugPrint('Mock: Creating user profile for ${user.email}');
  // }

  Future<void> _awardSignupBonus(String userId) async {
    debugPrint('Mock: Awarding signup bonus to user: $userId');
  }

  Future<void> _updateLastLogin() async {
    if (_currentUser != null) {
      // Simulate updating last login
      await _secureStorage.write(
        key: 'last_activity',
        value: DateTime.now().toIso8601String(),
      );
      debugPrint('Mock: Updated last login for ${_currentUser!.email}');
    }
  }

  void _startSessionTimer() {
    _stopSessionTimer();
    _sessionTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkSessionTimeout();
    });
  }

  void _stopSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = null;
  }

  Future<void> _checkSessionTimeout() async {
    try {
      final lastActivity = await _secureStorage.read(key: 'last_activity');
      if (lastActivity != null) {
        final lastActivityTime = DateTime.parse(lastActivity);
        final now = DateTime.now();
        
        if (now.difference(lastActivityTime) > _sessionTimeout) {
          await signOut();
          debugPrint('Mock: Session timed out. User signed out.');
        }
      }
    } catch (e) {
      debugPrint('Mock Session timeout check error: $e');
    }
  }

  Future<bool> _isAccountLocked(String email) async {
    // Mock account locking
    final attempts = await _secureStorage.read(key: 'failed_attempts_$email');
    final lockTime = await _secureStorage.read(key: 'lock_time_$email');
    
    if (attempts != null && lockTime != null) {
      final attemptCount = int.parse(attempts);
      final lockDateTime = DateTime.parse(lockTime);
      
      if (attemptCount >= _maxLoginAttempts) {
        final now = DateTime.now();
        if (now.difference(lockDateTime) < _lockoutDuration) {
          debugPrint('Mock: Account for $email is locked.');
          return true;
        } else {
          // Lockout duration passed, clear attempts
          await _clearFailedLoginAttempts(email);
        }
      }
    }
    return false;
  }

  Future<void> _trackFailedLoginAttempt(String email) async {
    int attempts = int.parse(await _secureStorage.read(key: 'failed_attempts_$email') ?? '0');
    attempts++;
    await _secureStorage.write(key: 'failed_attempts_$email', value: attempts.toString());
    if (attempts >= _maxLoginAttempts) {
      await _secureStorage.write(key: 'lock_time_$email', value: DateTime.now().toIso8601String());
      debugPrint('Mock: Account for $email locked due to $attempts failed attempts.');
    }
  }

  Future<void> _clearFailedLoginAttempts(String email) async {
    await _secureStorage.delete(key: 'failed_attempts_$email');
    await _secureStorage.delete(key: 'lock_time_$email');
    debugPrint('Mock: Cleared failed login attempts for $email.');
  }

  // Dispose method
  @override
  void dispose() {
    // _authStateSubscription?.cancel(); // No Firebase subscription
    _sessionTimer?.cancel();
    super.dispose();
  }
}
