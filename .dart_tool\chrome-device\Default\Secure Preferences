{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.157\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.157\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.157\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.157\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "CC314BA2EB4D0020C3B643ACBE618F1EA592D88384D7A783C8902371779BE174"}, "extensions": {"ui": {"developer_mode": "C7CA55B47B780A86A015D97EE597A78FBCE313AC22239D56096A748838A5AE41"}}, "homepage": "687117B8D5C568BD6B8ACF64D6C45702E3CE0D4593B7CA43FA65AD71FA77F76B", "homepage_is_newtabpage": "30F5CCA99863E3DCA5D75C06AEA6E557321729DA939ACCAA6B004CBB1DEEF4FC", "session": {"restore_on_startup": "825BF338CCB81D5FFB33ADA0587C89CBA8472FC16DCF34A20D3CF3CE4BDF7B56", "startup_urls": "A702542077A757BE69F04579F3BC150C4F88969DED13F8A1F61A28EFAAE409FF"}}, "browser": {"show_home_button": "1AB333E14134C7F64C8F6116AE1065621CFF5171CCB40489DA0B8566D7221379"}, "default_search_provider_data": {"template_url_data": "476704D0DABC61170F208E887E4BBA4C49C6488D877533A4E8EFF33269ECE4ED"}, "enterprise_signin": {"policy_recovery_token": "C1823C2B52CF1BD47C3B9A37B89AD48986E6474D9AEB44A5982A6C29AB705C2C"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "1B74583350AA8C2ACE2DF43465F506C694A5174EA30E997DA5F1AE2050D0781A", "fignfifoniblkonapihmkfakmlgkbkcf": "D3E16ECEF6BC0717600CD1F850FD38D4F0F1ECDDA9B6615BBF8B8FF93A90493C", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "EF9617DBF89E3CBCE2E7DDC804DE52ED2C5151E51C71BFB50925AB758264963F", "nkeimhogjdpnpccoofpliimaahmaaome": "2DAC9965C0D7DA0E82CC2DAF41AA72D3B75349BE5B4752E07570842803E68201"}, "ui": {"developer_mode": "96DC13AC69FB526A03F290D64AAE5A443B805CDA4640F06E216C85C261A5F667"}}, "google": {"services": {"account_id": "0B5D20F8256914BAF310D939A58B6300065C8DEC51F83D081E7971DECDF0960D", "last_signed_in_username": "F616B643192CA7A5BDAC7F5A9868D47EA757E7DD851EEAA512826342B4FA8959", "last_username": "C0FD95AEDE7DE4547FCE346E31573E284F853A9DEEFE2300C2E07CEA9F64799F"}}, "homepage": "12F39287D34B476C94BF0D5E3673FCEAE1113729779B7623C71C2EA6E660D954", "homepage_is_newtabpage": "6754ADB30AD441F26FA065961846268CC26958C0AB49E51EE5FCC2EC97261F14", "media": {"cdm": {"origin_data": "3FB37587F55AD827EF806620ABCBE32F51A6590E68D81B66D15E09A8208D8051"}, "storage_id_salt": "03DAE1457BE00FB8958CAEF74C536AC9AA736572B0CC2C711086AF744A3BBD74"}, "module_blocklist_cache_md5_digest": "DC304DF72AB5A1E99E6346D2DD73E8CF7E1D3035FA917FA139DF27F5F0574F1A", "pinned_tabs": "9D6908CD1DDDDD6F3E45932D5E92E3AA5CE46F43A0F7E9516B8317F86F4D1310", "prefs": {"preference_reset_time": "F4D15A2BF5FCFD85E6B86F8A719C820F9F6BC827FFCADC72C9C6D61FF3D59321"}, "safebrowsing": {"incidents_sent": "853D8C442F0C581FD390E73E797AEA2C5D5CA78D15CE403B21CC8EE6D52EE7E8"}, "search_provider_overrides": "2EEDB471D4ABC3D18871C6A8EA4DD4147B610A359700B64D806AF27B76688D87", "session": {"restore_on_startup": "D44FEA031B29405451EC3EAEF34DDDF4EAE0736BBBE0670654E077439D3585A3", "startup_urls": "F5E7A16450C306AC17F44F13A0D49F760350337A3C2385613A09E7137C2787B5"}}, "super_mac": "EE669E1D14ACC37AF15B696577DB9827348C01665075DE1426A44D19D36485D2"}, "safebrowsing": {"incidents_sent": {"1": {"account_values.browser.show_home_button": "**********", "account_values.session.restore_on_startup": "**********", "browser.show_home_button": "**********", "default_search_provider_data.template_url_data": "**********", "extensions.settings": "**********", "google.services.account_id": "**********", "google.services.last_signed_in_username": "**********", "google.services.last_username": "**********", "media.cdm.origin_data": "**********", "safebrowsing.incidents_sent": "*********", "session.restore_on_startup": "**********"}}}}