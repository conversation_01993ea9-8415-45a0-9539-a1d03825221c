# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/

# IntelliJ
*.iml
*.ipr
*.iws
.idea/

# Flutter
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
/ios/Flutter/App.framework
/ios/Flutter/Flutter.framework
/ios/Flutter/Generated.xcconfig
/ios/Flutter/flutter_export_environment.sh
/ios/Runner/GeneratedPluginRegistrant.*
/ios/Runner/Info.plist
/ios/Runner/Runner-Bridging-Header.h
/ios/Runner.xcworkspace/contents.xcworkspacedata
/ios/Runner.xcworkspace/xcshareddata/
/ios/.symlinks/
/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
/android/key.properties
/android/local.properties
/android/.gradle/
/android/app/build/
/android/build/
/android/gradle/
/android/gradlew
/android/gradlew.bat
/android/app/src/main/res/values/strings.xml
/android/app/src/main/res/mipmap-*/ic_launcher.png
/web/
/linux/
/macos/
/windows/
