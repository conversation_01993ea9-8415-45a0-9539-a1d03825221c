import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/translations.dart';

enum Language { english, french }

class LanguageService extends ChangeNotifier {
  static const String _languageKey = 'language';

  Language _language = Language.english;

  // Singleton pattern
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  // Getters
  Language get language => _language;

  // Initialize language service
  Future<void> initialize() async {
    await _loadLanguage();
  }

  // Load saved language
  Future<void> _loadLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey);

      if (savedLanguage != null) {
        _language = Language.values.firstWhere(
          (language) => language.toString() == savedLanguage,
          orElse: () => Language.english,
        );
      }
    } catch (e) {
      debugPrint('Error loading language: $e');
      _language = Language.english;
    }
  }

  // Save language
  Future<void> _saveLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, _language.toString());
    } catch (e) {
      debugPrint('Error saving language: $e');
    }
  }

  // Set language
  Future<void> setLanguage(Language language) async {
    if (_language != language) {
      _language = language;
      await _saveLanguage();
      notifyListeners();
    }
  }

  // Get language display name
  String getLanguageDisplayName(Language language) {
    switch (language) {
      case Language.english:
        return 'English';
      case Language.french:
        return 'Français';
    }
  }

  // Get current language display name
  String get currentLanguageDisplayName {
    return getLanguageDisplayName(_language);
  }

  // Get translated text
  String getTranslatedText(String englishText) {
    switch (_language) {
      case Language.french:
        return frenchTranslations[englishText] ?? englishText;
      default:
        return englishText;
    }
  }
}