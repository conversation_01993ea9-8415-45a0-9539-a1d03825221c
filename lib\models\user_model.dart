// import 'package:cloud_firestore/cloud_firestore.dart'; // Temporarily disabled for web compatibility

/// 👤 User Model for CHICA'S Chicken App
/// Represents a user with all their profile information and preferences
class UserModel {
  final String uid;
  final String email;
  final String name;
  final String? phone;
  final String? profileImageUrl;
  final bool emailVerified;
  final List<Address> addresses;
  final UserPreferences preferences;
  final LoyaltyInfo loyaltyInfo;
  final DateTime createdAt;
  final DateTime lastLogin;
  final DateTime? updatedAt;

  const UserModel({
    required this.uid,
    required this.email,
    required this.name,
    this.phone,
    this.profileImageUrl,
    required this.emailVerified,
    required this.addresses,
    required this.preferences,
    required this.loyaltyInfo,
    required this.createdAt,
    required this.lastLogin,
    this.updatedAt,
  });

  /// Create UserModel from JSON data
  factory UserModel.fromJson(Map<String, dynamic> data) {
    return UserModel(
      uid: data['uid'] ?? '',
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      phone: data['phone'],
      profileImageUrl: data['profileImageUrl'],
      emailVerified: data['emailVerified'] ?? false,
      addresses: (data['addresses'] as List<dynamic>?)
          ?.map((addr) => Address.fromJson(addr as Map<String, dynamic>))
          .toList() ?? [],
      preferences: UserPreferences.fromJson(data['preferences'] ?? {}),
      loyaltyInfo: LoyaltyInfo.fromJson(data['loyaltyInfo'] ?? {}),
      createdAt: data['createdAt'] != null
          ? DateTime.parse(data['createdAt'])
          : DateTime.now(),
      lastLogin: data['lastLogin'] != null
          ? DateTime.parse(data['lastLogin'])
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null
          ? DateTime.parse(data['updatedAt'])
          : null,
    );
  }

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'phone': phone,
      'profileImageUrl': profileImageUrl,
      'emailVerified': emailVerified,
      'addresses': addresses.map((addr) => addr.toJson()).toList(),
      'preferences': preferences.toJson(),
      'loyaltyInfo': loyaltyInfo.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  UserModel copyWith({
    String? name,
    String? phone,
    String? profileImageUrl,
    bool? emailVerified,
    List<Address>? addresses,
    UserPreferences? preferences,
    LoyaltyInfo? loyaltyInfo,
    DateTime? lastLogin,
    DateTime? updatedAt,
  }) {
    return UserModel(
      uid: uid,
      email: email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      emailVerified: emailVerified ?? this.emailVerified,
      addresses: addresses ?? this.addresses,
      preferences: preferences ?? this.preferences,
      loyaltyInfo: loyaltyInfo ?? this.loyaltyInfo,
      createdAt: createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// 📍 Address Model
class Address {
  final String id;
  final String label; // Home, Work, etc.
  final String street;
  final String city;
  final String province;
  final String postalCode;
  final String country;
  final bool isDefault;
  final double? latitude;
  final double? longitude;

  const Address({
    required this.id,
    required this.label,
    required this.street,
    required this.city,
    required this.province,
    required this.postalCode,
    required this.country,
    required this.isDefault,
    this.latitude,
    this.longitude,
  });

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      id: json['id'] ?? '',
      label: json['label'] ?? '',
      street: json['street'] ?? '',
      city: json['city'] ?? '',
      province: json['province'] ?? '',
      postalCode: json['postalCode'] ?? '',
      country: json['country'] ?? 'Canada',
      isDefault: json['isDefault'] ?? false,
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label': label,
      'street': street,
      'city': city,
      'province': province,
      'postalCode': postalCode,
      'country': country,
      'isDefault': isDefault,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

/// ⚙️ User Preferences Model
class UserPreferences {
  final List<String> favoriteItems;
  final List<String> dietaryRestrictions;
  final bool pushNotifications;
  final bool emailNotifications;
  final bool smsNotifications;
  final String preferredLanguage;
  final bool biometricAuth;
  final String theme; // light, dark, system

  const UserPreferences({
    required this.favoriteItems,
    required this.dietaryRestrictions,
    required this.pushNotifications,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.preferredLanguage,
    required this.biometricAuth,
    required this.theme,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      favoriteItems: List<String>.from(json['favoriteItems'] ?? []),
      dietaryRestrictions: List<String>.from(json['dietaryRestrictions'] ?? []),
      pushNotifications: json['pushNotifications'] ?? true,
      emailNotifications: json['emailNotifications'] ?? true,
      smsNotifications: json['smsNotifications'] ?? false,
      preferredLanguage: json['preferredLanguage'] ?? 'en',
      biometricAuth: json['biometricAuth'] ?? false,
      theme: json['theme'] ?? 'system',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'favoriteItems': favoriteItems,
      'dietaryRestrictions': dietaryRestrictions,
      'pushNotifications': pushNotifications,
      'emailNotifications': emailNotifications,
      'smsNotifications': smsNotifications,
      'preferredLanguage': preferredLanguage,
      'biometricAuth': biometricAuth,
      'theme': theme,
    };
  }
}

/// 🏆 Loyalty Information Model
class LoyaltyInfo {
  final int points;
  final String tier; // Bronze, Silver, Gold, Platinum
  final int totalSpent; // in cents
  final int totalOrders;
  final DateTime? lastEarnedDate;
  final List<String> availableRewards;

  const LoyaltyInfo({
    required this.points,
    required this.tier,
    required this.totalSpent,
    required this.totalOrders,
    this.lastEarnedDate,
    required this.availableRewards,
  });

  factory LoyaltyInfo.fromJson(Map<String, dynamic> json) {
    return LoyaltyInfo(
      points: json['points'] ?? 0,
      tier: json['tier'] ?? 'Bronze',
      totalSpent: json['totalSpent'] ?? 0,
      totalOrders: json['totalOrders'] ?? 0,
      lastEarnedDate: json['lastEarnedDate'] != null
          ? DateTime.parse(json['lastEarnedDate'])
          : null,
      availableRewards: List<String>.from(json['availableRewards'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'points': points,
      'tier': tier,
      'totalSpent': totalSpent,
      'totalOrders': totalOrders,
      'lastEarnedDate': lastEarnedDate?.toIso8601String(),
      'availableRewards': availableRewards,
    };
  }
}
