<svg width="144" height="144" viewBox="0 0 144 144" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="geminiGrad144" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    <filter id="glow144">
      <feGaussianBlur stdDeviation="7.2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with gradient -->
  <rect x="0" y="0" width="144" height="144" rx="27" fill="url(#geminiGrad144)"/>
  
  <!-- Gemini constellation symbol -->
  <g transform="translate(72,72)" filter="url(#glow144)">
    <!-- Top star shape -->
    <path d="M0,-45 L11.25,0 L-11.25,0 Z" fill="white" opacity="0.95"/>
    <!-- Bottom star shape -->
    <path d="M0,45 L-11.25,0 L11.25,0 Z" fill="white" opacity="0.85"/>
    <!-- Connecting elements -->
    <circle cx="0" cy="-22.5" r="6.75" fill="white" opacity="0.9"/>
    <circle cx="0" cy="22.5" r="6.75" fill="white" opacity="0.9"/>
    <line x1="0" y1="-22.5" x2="0" y2="22.5" stroke="white" stroke-width="4.5" opacity="0.7"/>
  </g>
</svg>