# 📸 Social Rewards Feature - Testing Guide

## 🎯 Overview
This guide helps you test the new Social Rewards feature in the Chica's Chicken Flutter app. Perfect for a 10-year-old developer to follow step-by-step!

## 🛠️ Prerequisites
- Visual Studio Code with Flutter extension
- Android Studio or Xcode for device testing
- Physical device or emulator with camera
- Social media apps installed (Instagram, Facebook, TikTok) for full testing

## 🚀 Getting Started

### 1. 📱 Setup Your Testing Environment
```bash
# Open terminal in VS Code (Ctrl+` or Cmd+`)
flutter doctor

# Make sure everything shows checkmarks ✅
# If not, follow the instructions to fix issues
```

### 2. 📦 Install Dependencies
```bash
# Run this command in the terminal
flutter pub get

# You should see "Got dependencies!" message
```

### 3. 🔧 Check Permissions Configuration
- **Android**: Check `android/app/src/main/AndroidManifest.xml` has camera permissions
- **iOS**: Check `ios/Runner/Info.plist` has camera usage descriptions

## 🧪 Testing Steps

### Phase 1: Basic Navigation Testing

#### Test 1.1: Access Social Rewards
1. 🏃 Run the app: `flutter run`
2. 🎮 Navigate to "Games" tab in bottom navigation
3. 📸 Look for the orange "SOCIAL REWARDS" card at the top
4. ✅ **Expected**: Card shows with camera icon and "NEW!" badge
5. 👆 Tap the Social Rewards card
6. ✅ **Expected**: Opens Social Rewards screen with camera interface

#### Test 1.2: App Bar Features
1. 🏆 Tap the leaderboard icon (trophy) in top-right
2. ✅ **Expected**: Opens leaderboard (empty initially)
3. ⬅️ Go back to Social Rewards screen
4. 🔒 Tap the privacy icon (shield) in top-right
5. ✅ **Expected**: Opens privacy policy screen
6. ✅ **Expected**: Privacy policy shows GDPR/CCPA compliant information

### Phase 2: Camera Permission Testing

#### Test 2.1: First-Time Camera Access
1. 📸 Tap "Take Photo" button
2. ✅ **Expected**: Permission dialog appears asking for camera access
3. ✅ **Expected**: Dialog explains why camera is needed
4. 👍 Grant camera permission
5. ✅ **Expected**: Camera interface opens

#### Test 2.2: Permission Denied Scenario
1. 🚫 If you denied permission, tap "Take Photo" again
2. ✅ **Expected**: Helpful dialog explains how to enable camera in settings
3. ⚙️ Follow instructions to enable camera in device settings
4. 🔄 Return to app and try again

### Phase 3: Photo/Video Capture Testing

#### Test 3.1: Photo Capture
1. 📸 Tap "Take Photo" button
2. ✅ **Expected**: Camera viewfinder opens
3. 📷 Take a photo of some food (or anything colorful)
4. ✅ **Expected**: Photo appears in preview screen
5. ✅ **Expected**: Brightness slider works to adjust photo
6. ✅ **Expected**: Hashtags are pre-filled: "#ChicasChickenBites #QSRRewards"

#### Test 3.2: Video Capture
1. 🎥 Tap "Take Video" button
2. ✅ **Expected**: Camera opens in video mode
3. 🎬 Record a short video (5-10 seconds)
4. ⏹️ Stop recording
5. ✅ **Expected**: Video appears in preview screen
6. ✅ **Expected**: Can play/pause video preview

#### Test 3.3: Retake Functionality
1. 📸 After capturing media, tap "Retake" button
2. ✅ **Expected**: Returns to camera interface
3. ✅ **Expected**: Previous media is cleared
4. 📷 Take another photo/video
5. ✅ **Expected**: New media appears in preview

### Phase 4: Preview Screen Testing

#### Test 4.1: Hashtag Editing
1. 📝 In preview screen, edit the hashtags
2. ✅ **Expected**: Can add/remove hashtags
3. ✅ **Expected**: Character count updates
4. ✅ **Expected**: Hashtags save when typing

#### Test 4.2: Watermark Toggle
1. 🏷️ Toggle the "Add Watermark" switch
2. ✅ **Expected**: Switch changes state
3. ✅ **Expected**: Preview shows watermark will be added to share text

#### Test 4.3: Brightness Filter
1. 🌞 Move the brightness slider left and right
2. ✅ **Expected**: Photo brightness changes in real-time
3. ✅ **Expected**: Slider works smoothly
4. ✅ **Expected**: Changes are visible immediately

### Phase 5: Social Sharing Testing

#### Test 5.1: Instagram Sharing
1. 📸 After capturing media, tap "Share to Instagram"
2. ✅ **Expected**: Device share menu opens
3. ✅ **Expected**: Instagram appears in share options (if installed)
4. 📱 Complete sharing process
5. ✅ **Expected**: Returns to app with success dialog
6. ✅ **Expected**: Success dialog shows "10 points earned"

#### Test 5.2: Facebook Sharing
1. 📘 Tap "Share to Facebook"
2. ✅ **Expected**: Share menu opens with Facebook option
3. 📱 Complete sharing (or cancel to test)
4. ✅ **Expected**: Success dialog appears

#### Test 5.3: General Sharing
1. 📤 Tap "Share to Other Apps"
2. ✅ **Expected**: Full device share menu opens
3. ✅ **Expected**: Shows all available apps (Messages, Email, etc.)
4. 📧 Try sharing via email or messages
5. ✅ **Expected**: Media and hashtags are included

### Phase 6: Loyalty Points Testing

#### Test 6.1: Points Award System
1. 📸 Share content successfully
2. ✅ **Expected**: Success dialog shows "10 points earned"
3. 🏠 Navigate to home screen or loyalty section
4. ✅ **Expected**: Points balance increased by 10
5. 🔄 Repeat sharing process
6. ✅ **Expected**: Points continue to accumulate

#### Test 6.2: Points Persistence
1. 📱 Close and reopen the app
2. ✅ **Expected**: Points balance is saved
3. ✅ **Expected**: Previous shares are remembered

### Phase 7: Leaderboard Testing

#### Test 7.1: First Share
1. 📸 Share your first piece of content
2. 🏆 Open leaderboard
3. ✅ **Expected**: Shows "You" with 1 share and 10 points
4. ✅ **Expected**: Shows gold medal (🥇) for first place

#### Test 7.2: Multiple Shares
1. 📸 Share 2-3 more pieces of content
2. 🏆 Check leaderboard again
3. ✅ **Expected**: Share count and points update correctly
4. ✅ **Expected**: Stats in header update (Total Sharers, Total Shares, Points Earned)

#### Test 7.3: Leaderboard Persistence
1. 📱 Close and reopen app
2. 🏆 Check leaderboard
3. ✅ **Expected**: Data is saved and restored correctly

### Phase 8: Error Handling Testing

#### Test 8.1: No Camera Available
1. 🖥️ Test on device/emulator without camera
2. ✅ **Expected**: Helpful error message appears
3. ✅ **Expected**: App doesn't crash

#### Test 8.2: Storage Full
1. 📱 If device storage is very low
2. ✅ **Expected**: Graceful error handling
3. ✅ **Expected**: Clear error message to user

#### Test 8.3: No Internet Connection
1. 📶 Turn off WiFi and mobile data
2. 📸 Try sharing content
3. ✅ **Expected**: Sharing still works (uses device share menu)
4. ✅ **Expected**: Points are saved locally

## 🐛 Common Issues & Solutions

### Issue 1: Camera Permission Denied
**Solution**: Go to device Settings > Apps > Chica's Chicken > Permissions > Enable Camera

### Issue 2: Share Menu Doesn't Open
**Solution**: Make sure target apps (Instagram, Facebook) are installed

### Issue 3: Points Not Saving
**Solution**: Check device storage space, restart app

### Issue 4: Leaderboard Empty
**Solution**: Share at least one piece of content first

## 📊 Test Results Checklist

### ✅ Basic Functionality
- [ ] Social Rewards button appears on Games screen
- [ ] Camera interface opens correctly
- [ ] Photo capture works
- [ ] Video capture works
- [ ] Preview screen shows media correctly

### ✅ Sharing Features
- [ ] Instagram sharing works
- [ ] Facebook sharing works
- [ ] General sharing works
- [ ] Hashtags are included in shares
- [ ] Watermark text is added when enabled

### ✅ Loyalty System
- [ ] 10 points awarded per share
- [ ] Points persist after app restart
- [ ] Success dialog shows correct points

### ✅ Leaderboard
- [ ] Updates after sharing
- [ ] Shows correct stats
- [ ] Persists data correctly
- [ ] Displays rankings properly

### ✅ Privacy & Permissions
- [ ] Camera permission requested properly
- [ ] Privacy policy accessible and complete
- [ ] User-friendly permission explanations

### ✅ Error Handling
- [ ] Graceful handling of missing camera
- [ ] Clear error messages
- [ ] App doesn't crash on errors

## 🎉 Success Criteria
- All checkboxes above are marked ✅
- No crashes during testing
- User experience is smooth and intuitive
- Privacy requirements are met
- Points system works correctly

## 📞 Need Help?
If you encounter issues during testing:
1. Check the Flutter console for error messages
2. Try `flutter clean` and `flutter pub get`
3. Restart your device/emulator
4. Check device permissions in settings

Happy testing! 🚀📱
