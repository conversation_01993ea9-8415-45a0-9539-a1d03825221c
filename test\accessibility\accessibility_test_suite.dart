import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qsr_app/main.dart' as app;
import 'package:qsr_app/services/accessibility_service.dart';
import 'package:qsr_app/utils/color_contrast_validator.dart';
import 'package:qsr_app/widgets/accessibility_widgets.dart';

/// 🧪 Comprehensive Accessibility Test Suite for CHICA'S Chicken
/// Tests WCAG 2.1 AA compliance across all app features
/// 
/// Test Categories:
/// 1. Color Contrast Validation
/// 2. Touch Target Size Compliance
/// 3. Screen Reader Support
/// 4. Keyboard Navigation
/// 5. Focus Management
/// 6. Semantic Structure
/// 7. Form Accessibility
/// 8. Error Handling and Announcements

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('WCAG 2.1 AA Accessibility Tests', () {
    
    group('Color Contrast Tests', () {
      testWidgets('Primary colors meet WCAG AA contrast requirements', (tester) async {
        const chicaOrange = Color(0xFFFF5C22);
        const chicaRed = Color(0xFF9B1C24);
        
        // Test primary color on white background
        expect(
          ColorContrastValidator.meetsWCAGAA(chicaOrange, Colors.white),
          isTrue,
          reason: 'CHICA Orange should have sufficient contrast on white background',
        );
        
        // Test white text on primary color
        expect(
          ColorContrastValidator.meetsWCAGAA(Colors.white, chicaOrange),
          isTrue,
          reason: 'White text should have sufficient contrast on CHICA Orange',
        );
        
        // Test secondary color contrast
        expect(
          ColorContrastValidator.meetsWCAGAA(Colors.white, chicaRed),
          isTrue,
          reason: 'White text should have sufficient contrast on CHICA Red',
        );
      });

      testWidgets('Text colors meet minimum contrast ratios', (tester) async {
        app.main();
        await tester.pumpAndSettle();
        
        // Find text widgets and test their contrast
        final textWidgets = find.byType(Text);
        expect(textWidgets, findsWidgets);
        
        for (int i = 0; i < textWidgets.evaluate().length && i < 10; i++) {
          final textWidget = tester.widget<Text>(textWidgets.at(i));
          if (textWidget.style?.color != null) {
            final textColor = textWidget.style!.color!;
            const backgroundColor = Colors.white; // Assuming white background
            
            final contrastRatio = ColorContrastValidator.calculateContrastRatio(
              textColor, 
              backgroundColor,
            );
            
            expect(
              contrastRatio,
              greaterThanOrEqualTo(4.5),
              reason: 'Text contrast ratio should meet WCAG AA standard (4.5:1)',
            );
          }
        }
      });

      testWidgets('High contrast mode provides sufficient contrast', (tester) async {
        app.main();
        await tester.pumpAndSettle();
        
        // Simulate high contrast mode
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          'flutter/accessibility',
          null,
          (data) {},
        );
        
        await tester.pumpAndSettle();
        
        // Test that high contrast colors are applied
        final accessibilityService = AccessibilityService();
        expect(accessibilityService.isHighContrastEnabled, isTrue);
      });
    });

    group('Touch Target Size Tests', () {
      testWidgets('All interactive elements meet minimum touch target size', (tester) async {
        app.main();
        await tester.pumpAndSettle();
        
        // Find all button widgets
        final buttons = find.byType(ElevatedButton);
        expect(buttons, findsWidgets);
        
        for (int i = 0; i < buttons.evaluate().length; i++) {
          final buttonWidget = tester.widget<ElevatedButton>(buttons.at(i));
          final renderBox = tester.renderObject<RenderBox>(buttons.at(i));
          
          expect(
            renderBox.size.width,
            greaterThanOrEqualTo(44.0),
            reason: 'Button width should meet minimum touch target size (44px)',
          );
          
          expect(
            renderBox.size.height,
            greaterThanOrEqualTo(44.0),
            reason: 'Button height should meet minimum touch target size (44px)',
          );
        }
      });

      testWidgets('Custom accessible buttons meet touch target requirements', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WCAGButton(
                onPressed: () {},
                child: const Text('Test Button'),
              ),
            ),
          ),
        );
        
        final buttonFinder = find.byType(WCAGButton);
        expect(buttonFinder, findsOneWidget);
        
        final renderBox = tester.renderObject<RenderBox>(buttonFinder);
        expect(renderBox.size.width, greaterThanOrEqualTo(44.0));
        expect(renderBox.size.height, greaterThanOrEqualTo(44.0));
      });
    });

    group('Screen Reader Support Tests', () {
      testWidgets('Semantic labels are properly set', (tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: AccessibleFormField(
                label: 'Email Address',
                hint: 'Enter your email',
                required: true,
                semanticLabel: 'Email address, required field',
              ),
            ),
          ),
        );
        
        final semantics = tester.getSemantics(find.byType(AccessibleFormField));
        expect(semantics.label, contains('Email address'));
        expect(semantics.label, contains('required'));
      });

      testWidgets('Live regions announce dynamic content changes', (tester) async {
        app.main();
        await tester.pumpAndSettle();
        
        // Test that error messages are announced
        final errorText = find.text('Error');
        if (errorText.evaluate().isNotEmpty) {
          final semantics = tester.getSemantics(errorText);
          expect(semantics.hasFlag(SemanticsFlag.isLiveRegion), isTrue);
        }
      });
    });

    group('Keyboard Navigation Tests', () {
      testWidgets('Tab navigation works correctly', (tester) async {
        app.main();
        await tester.pumpAndSettle();
        
        // Find focusable elements
        final focusableElements = find.byType(Focus);
        if (focusableElements.evaluate().isNotEmpty) {
          // Test tab navigation
          await tester.sendKeyEvent(LogicalKeyboardKey.tab, platform: 'windows');
          await tester.pumpAndSettle();
          
          // Verify focus moved
          expect(find.byType(Focus), findsWidgets);
        }
      });

      testWidgets('Enter key activates buttons', (tester) async {
        bool buttonPressed = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WCAGButton(
                onPressed: () => buttonPressed = true,
                child: const Text('Test Button'),
              ),
            ),
          ),
        );
        
        // Focus the button
        await tester.tap(find.byType(WCAGButton));
        await tester.pumpAndSettle();
        
        // Press Enter
        await tester.sendKeyEvent(LogicalKeyboardKey.enter, platform: 'windows');
        await tester.pumpAndSettle();
        
        expect(buttonPressed, isTrue);
      });
    });

    group('Form Accessibility Tests', () {
      testWidgets('Form fields have proper labels and error handling', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AccessibleFormField(
                label: 'Password',
                hint: 'Enter your password',
                required: true,
                validator: (value) => value?.isEmpty == true ? 'Password is required' : null,
                errorText: 'Password is required',
              ),
            ),
          ),
        );
        
        final formField = find.byType(AccessibleFormField);
        expect(formField, findsOneWidget);
        
        // Check that error text is announced
        final errorText = find.text('Password is required');
        if (errorText.evaluate().isNotEmpty) {
          final semantics = tester.getSemantics(errorText);
          expect(semantics.hasFlag(SemanticsFlag.isLiveRegion), isTrue);
        }
      });

      testWidgets('Required fields are properly marked', (tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: AccessibleFormField(
                label: 'Email',
                required: true,
              ),
            ),
          ),
        );
        
        final semantics = tester.getSemantics(find.byType(AccessibleFormField));
        expect(semantics.label, contains('required'));
      });
    });

    group('Focus Management Tests', () {
      testWidgets('Focus is properly managed during navigation', (tester) async {
        app.main();
        await tester.pumpAndSettle();
        
        // Test focus management during screen transitions
        final loginButton = find.text('Sign In');
        if (loginButton.evaluate().isNotEmpty) {
          await tester.tap(loginButton);
          await tester.pumpAndSettle();
          
          // Verify focus is set appropriately on new screen
          expect(find.byType(Focus), findsWidgets);
        }
      });

      testWidgets('Focus indicators are visible', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WCAGButton(
                onPressed: () {},
                child: const Text('Focusable Button'),
              ),
            ),
          ),
        );
        
        // Focus the button
        await tester.tap(find.byType(WCAGButton));
        await tester.pumpAndSettle();
        
        // Verify focus indicator is present
        final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
        expect(button.focusNode?.hasFocus, isTrue);
      });
    });

    group('Semantic Structure Tests', () {
      testWidgets('Headings are properly structured', (tester) async {
        app.main();
        await tester.pumpAndSettle();
        
        // Find heading elements
        final headings = find.byWidgetPredicate((widget) {
          if (widget is Semantics) {
            return widget.properties.header ?? false;
          }
          return false;
        });
        
        expect(headings, findsWidgets);
      });

      testWidgets('Lists have proper semantic structure', (tester) async {
        app.main();
        await tester.pumpAndSettle();
        
        // Test menu items are properly structured as lists
        final listViews = find.byType(ListView);
        if (listViews.evaluate().isNotEmpty) {
          expect(listViews, findsWidgets);
        }
      });
    });

    group('Error Handling and Announcements Tests', () {
      testWidgets('Error messages are announced to screen readers', (tester) async {
        bool errorAnnounced = false;
        
        // Mock accessibility service
        final accessibilityService = AccessibilityService();
        
        // Test error announcement
        await accessibilityService.announce('Test error message');
        
        // In a real test, you would verify the announcement was made
        expect(accessibilityService.isScreenReaderEnabled, isNotNull);
      });

      testWidgets('Success messages are announced', (tester) async {
        app.main();
        await tester.pumpAndSettle();
        
        // Test success message announcement
        final snackBars = find.byType(SnackBar);
        if (snackBars.evaluate().isNotEmpty) {
          final snackBar = tester.widget<SnackBar>(snackBars.first);
          expect(snackBar.content, isA<Widget>());
        }
      });
    });

    group('Text Scaling Tests', () {
      testWidgets('App supports text scaling up to 200%', (tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: MediaQuery(
              data: MediaQueryData(textScaler: TextScaler.linear(2.0)),
              child: Scaffold(
                body: AccessibleText(
                  'Test text scaling',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ),
        );
        
        final textWidget = tester.widget<Text>(find.byType(Text));
        expect(textWidget.style?.fontSize, equals(32.0)); // 16 * 2.0
      });

      testWidgets('Layout remains usable with large text', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: MediaQuery(
              data: const MediaQueryData(textScaler: TextScaler.linear(2.0)),
              child: Scaffold(
                body: Column(
                  children: [
                    const AccessibleText('Header', style: TextStyle(fontSize: 24)),
                    const AccessibleText('Body text', style: TextStyle(fontSize: 16)),
                    WCAGButton(
                      onPressed: () {},
                      child: const Text('Button'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
        
        await tester.pumpAndSettle();
        
        // Verify no overflow errors
        expect(tester.takeException(), isNull);
      });
    });
  });
}

/// Helper function to run accessibility audit
Future<Map<String, dynamic>> runAccessibilityAudit(WidgetTester tester) async {
  final results = <String, dynamic>{};
  
  // Color contrast audit
  results['colorContrast'] = await _auditColorContrast(tester);
  
  // Touch target audit
  results['touchTargets'] = await _auditTouchTargets(tester);
  
  // Semantic structure audit
  results['semantics'] = await _auditSemantics(tester);
  
  return results;
}

Future<List<String>> _auditColorContrast(WidgetTester tester) async {
  final issues = <String>[];
  
  // Find all text widgets and check their contrast
  final textWidgets = find.byType(Text);
  for (int i = 0; i < textWidgets.evaluate().length; i++) {
    final textWidget = tester.widget<Text>(textWidgets.at(i));
    if (textWidget.style?.color != null) {
      final textColor = textWidget.style!.color!;
      const backgroundColor = Colors.white; // Simplified assumption
      
      if (!ColorContrastValidator.meetsWCAGAA(textColor, backgroundColor)) {
        issues.add('Text "${textWidget.data}" has insufficient contrast');
      }
    }
  }
  
  return issues;
}

Future<List<String>> _auditTouchTargets(WidgetTester tester) async {
  final issues = <String>[];
  
  // Check button sizes
  final buttons = find.byType(ElevatedButton);
  for (int i = 0; i < buttons.evaluate().length; i++) {
    final renderBox = tester.renderObject<RenderBox>(buttons.at(i));
    if (renderBox.size.width < 44.0 || renderBox.size.height < 44.0) {
      issues.add('Button at index $i does not meet minimum touch target size');
    }
  }
  
  return issues;
}

Future<List<String>> _auditSemantics(WidgetTester tester) async {
  final issues = <String>[];
  
  // Check for missing semantic labels
  final semanticsNodes = tester.binding.pipelineOwner.semanticsOwner?.rootSemanticsNode?.debugDescribeChildren();
  
  // This would be expanded to check for proper semantic structure
  
  return issues;
}
