# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
dist-ssr/
build/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
.tmp/

# Vite
.vite/

# Local Netlify folder
.netlify

# Claude specific
.claude/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.backup
*.bak

# Windows Zone.Identifier files
*:Zone.Identifier

# Development logs
dev.log
dev_*.log
*_dev.log

# Test files
test-*.html
test-*.js
test-*.jsx
*.test.js
*.test.jsx
*.spec.js
*.spec.jsx
TestApp.jsx

# Project data and sessions (exclude from git)
projects_data/
session_data/
user_uploads/