import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 🎨 WCAG 2.1 AA Color Contrast Validator
/// Provides comprehensive color contrast validation and adjustment utilities
/// to ensure compliance with accessibility standards
class ColorContrastValidator {
  // WCAG 2.1 AA contrast ratio requirements
  static const double _normalTextMinRatio = 4.5;
  static const double _largeTextMinRatio = 3.0;
  static const double _aaaMinRatio = 7.0;
  
  // Large text threshold (18pt regular or 14pt bold)
  static const double _largeTextSize = 18.0;
  static const double _boldLargeTextSize = 14.0;

  /// Calculate the contrast ratio between two colors
  /// Returns a value between 1 and 21, where 21 is maximum contrast
  static double calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateRelativeLuminance(color1);
    final luminance2 = _calculateRelativeLuminance(color2);
    
    final lighter = math.max(luminance1, luminance2);
    final darker = math.min(luminance1, luminance2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculate relative luminance of a color according to WCAG formula
  static double _calculateRelativeLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearize color component for luminance calculation
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return math.pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Check if color combination meets WCAG AA standards
  static bool meetsWCAGAA(
    Color foreground, 
    Color background, {
    bool isLargeText = false,
    bool isBold = false,
  }) {
    final ratio = calculateContrastRatio(foreground, background);
    final requiredRatio = _getRequiredRatio(isLargeText, isBold, false);
    return ratio >= requiredRatio;
  }

  /// Check if color combination meets WCAG AAA standards
  static bool meetsWCAGAAA(
    Color foreground, 
    Color background, {
    bool isLargeText = false,
    bool isBold = false,
  }) {
    final ratio = calculateContrastRatio(foreground, background);
    final requiredRatio = _getRequiredRatio(isLargeText, isBold, true);
    return ratio >= requiredRatio;
  }

  /// Get required contrast ratio based on text properties
  static double _getRequiredRatio(bool isLargeText, bool isBold, bool isAAA) {
    if (isAAA) {
      return isLargeText || isBold ? 4.5 : _aaaMinRatio;
    }
    return isLargeText || isBold ? _largeTextMinRatio : _normalTextMinRatio;
  }

  /// Determine if text size qualifies as "large text" for WCAG
  static bool isLargeText(double fontSize, {bool isBold = false}) {
    if (isBold) {
      return fontSize >= _boldLargeTextSize;
    }
    return fontSize >= _largeTextSize;
  }

  /// Get accessible foreground color for given background
  static Color getAccessibleForegroundColor(
    Color background, {
    bool isLargeText = false,
    bool isBold = false,
    bool preferDark = true,
    bool requireAAA = false,
  }) {
    const darkColor = Colors.black;
    const lightColor = Colors.white;
    
    // Test preferred color first
    final preferredColor = preferDark ? darkColor : lightColor;
    final alternativeColor = preferDark ? lightColor : darkColor;
    
    if (requireAAA) {
      if (meetsWCAGAAA(preferredColor, background, isLargeText: isLargeText, isBold: isBold)) {
        return preferredColor;
      }
      if (meetsWCAGAAA(alternativeColor, background, isLargeText: isLargeText, isBold: isBold)) {
        return alternativeColor;
      }
    } else {
      if (meetsWCAGAA(preferredColor, background, isLargeText: isLargeText, isBold: isBold)) {
        return preferredColor;
      }
      if (meetsWCAGAA(alternativeColor, background, isLargeText: isLargeText, isBold: isBold)) {
        return alternativeColor;
      }
    }
    
    // If neither black nor white works, adjust the background luminance
    return _adjustColorForContrast(background, preferredColor, isLargeText, isBold, requireAAA);
  }

  /// Adjust color to meet contrast requirements
  static Color _adjustColorForContrast(
    Color background,
    Color foreground,
    bool isLargeText,
    bool isBold,
    bool requireAAA,
  ) {
    final requiredRatio = _getRequiredRatio(isLargeText, isBold, requireAAA);
    final backgroundLuminance = _calculateRelativeLuminance(background);
    
    // Determine if we need a lighter or darker foreground
    final needsLighter = backgroundLuminance < 0.5;
    
    if (needsLighter) {
      // Make foreground lighter
      return _adjustColorLuminance(foreground, true, requiredRatio, backgroundLuminance);
    } else {
      // Make foreground darker
      return _adjustColorLuminance(foreground, false, requiredRatio, backgroundLuminance);
    }
  }

  /// Adjust color luminance to meet contrast ratio
  static Color _adjustColorLuminance(
    Color color,
    bool makeLighter,
    double requiredRatio,
    double backgroundLuminance,
  ) {
    var hsl = HSLColor.fromColor(color);
    
    // Binary search for the right lightness value
    double minL = makeLighter ? hsl.lightness : 0.0;
    double maxL = makeLighter ? 1.0 : hsl.lightness;
    
    for (int i = 0; i < 20; i++) { // Max 20 iterations
      final testL = (minL + maxL) / 2;
      final testColor = hsl.withLightness(testL).toColor();
      final testLuminance = _calculateRelativeLuminance(testColor);
      
      final lighter = math.max(testLuminance, backgroundLuminance);
      final darker = math.min(testLuminance, backgroundLuminance);
      final ratio = (lighter + 0.05) / (darker + 0.05);
      
      if (ratio >= requiredRatio) {
        if (makeLighter) {
          maxL = testL;
        } else {
          minL = testL;
        }
      } else {
        if (makeLighter) {
          minL = testL;
        } else {
          maxL = testL;
        }
      }
    }
    
    return hsl.withLightness((minL + maxL) / 2).toColor();
  }

  /// Generate high contrast color palette
  static Map<String, Color> generateHighContrastPalette(Color primaryColor) {
    return {
      'primary': primaryColor,
      'onPrimary': getAccessibleForegroundColor(primaryColor),
      'background': Colors.white,
      'onBackground': Colors.black,
      'surface': Colors.white,
      'onSurface': Colors.black,
      'error': const Color(0xFFD32F2F),
      'onError': Colors.white,
      'outline': Colors.black,
    };
  }

  /// Generate dark high contrast color palette
  static Map<String, Color> generateDarkHighContrastPalette(Color primaryColor) {
    return {
      'primary': primaryColor,
      'onPrimary': getAccessibleForegroundColor(primaryColor),
      'background': Colors.black,
      'onBackground': Colors.white,
      'surface': Colors.black,
      'onSurface': Colors.white,
      'error': const Color(0xFFFF5252),
      'onError': Colors.black,
      'outline': Colors.white,
    };
  }

  /// Validate entire color scheme for WCAG compliance
  static Map<String, bool> validateColorScheme(ColorScheme colorScheme) {
    return {
      'primary_onPrimary': meetsWCAGAA(colorScheme.onPrimary, colorScheme.primary),
      'secondary_onSecondary': meetsWCAGAA(colorScheme.onSecondary, colorScheme.secondary),
      'surface_onSurface': meetsWCAGAA(colorScheme.onSurface, colorScheme.surface),
      'background_onBackground': meetsWCAGAA(colorScheme.onSurface, colorScheme.surface),
      'error_onError': meetsWCAGAA(colorScheme.onError, colorScheme.error),
    };
  }

  /// Get contrast ratio grade (AA, AAA, or FAIL)
  static String getContrastGrade(
    Color foreground, 
    Color background, {
    bool isLargeText = false,
    bool isBold = false,
  }) {
    final ratio = calculateContrastRatio(foreground, background);
    
    if (meetsWCAGAAA(foreground, background, isLargeText: isLargeText, isBold: isBold)) {
      return 'AAA';
    } else if (meetsWCAGAA(foreground, background, isLargeText: isLargeText, isBold: isBold)) {
      return 'AA';
    } else {
      return 'FAIL';
    }
  }

  /// Generate accessible color variants
  static List<Color> generateAccessibleVariants(Color baseColor, Color background) {
    final variants = <Color>[];
    
    // Original color if it passes
    if (meetsWCAGAA(baseColor, background)) {
      variants.add(baseColor);
    }
    
    // Darker variants
    var hsl = HSLColor.fromColor(baseColor);
    for (double lightness = hsl.lightness - 0.1; lightness >= 0.0; lightness -= 0.1) {
      final variant = hsl.withLightness(lightness).toColor();
      if (meetsWCAGAA(variant, background)) {
        variants.add(variant);
        break;
      }
    }
    
    // Lighter variants
    for (double lightness = hsl.lightness + 0.1; lightness <= 1.0; lightness += 0.1) {
      final variant = hsl.withLightness(lightness).toColor();
      if (meetsWCAGAA(variant, background)) {
        variants.add(variant);
        break;
      }
    }
    
    return variants;
  }

  /// Create accessible text style with proper contrast
  static TextStyle createAccessibleTextStyle({
    required Color backgroundColor,
    required double fontSize,
    FontWeight? fontWeight,
    bool requireAAA = false,
  }) {
    final isBold = fontWeight != null && fontWeight.index >= FontWeight.w600.index;
    final isLarge = isLargeText(fontSize, isBold: isBold);
    
    final textColor = getAccessibleForegroundColor(
      backgroundColor,
      isLargeText: isLarge,
      isBold: isBold,
      requireAAA: requireAAA,
    );
    
    return TextStyle(
      color: textColor,
      fontSize: fontSize,
      fontWeight: fontWeight,
    );
  }
}

/// Extension to add contrast validation to Color class
extension ColorContrastExtension on Color {
  /// Check if this color has sufficient contrast with another color
  bool hasGoodContrastWith(
    Color other, {
    bool isLargeText = false,
    bool isBold = false,
  }) {
    return ColorContrastValidator.meetsWCAGAA(
      this, 
      other, 
      isLargeText: isLargeText, 
      isBold: isBold,
    );
  }
  
  /// Get contrast ratio with another color
  double contrastRatioWith(Color other) {
    return ColorContrastValidator.calculateContrastRatio(this, other);
  }
  
  /// Get accessible foreground color for this background
  Color getAccessibleForeground({
    bool isLargeText = false,
    bool isBold = false,
    bool preferDark = true,
  }) {
    return ColorContrastValidator.getAccessibleForegroundColor(
      this,
      isLargeText: isLargeText,
      isBold: isBold,
      preferDark: preferDark,
    );
  }
}
