{"name": "chicas-chicken-backend", "version": "1.0.0", "description": "Backend API for Chica's Chicken QSR ordering app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "setup": "node scripts/setup.js", "logs": "tail -f logs/combined.log"}, "keywords": ["qsr", "restaurant", "api", "nodejs"], "author": "TYYO8888", "license": "MIT", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "firebase-admin": "^11.11.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "redis": "^5.5.6", "stripe": "^14.7.0", "winston": "^3.11.0", "ws": "^8.18.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}