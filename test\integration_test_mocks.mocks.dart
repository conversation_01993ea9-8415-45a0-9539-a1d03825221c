// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in qsr_app/test/integration_test_mocks.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:qsr_app/services/notification_service.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [NotificationService].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockNotificationService extends _i1.Mock
    implements _i2.NotificationService {
  MockNotificationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<Map<String, dynamic>> get notificationStream =>
      (super.noSuchMethod(
        Invocation.getter(#notificationStream),
        returnValue: _i3.Stream<Map<String, dynamic>>.empty(),
      ) as _i3.Stream<Map<String, dynamic>>);

  @override
  _i3.Stream<Map<String, dynamic>> get orderUpdateStream => (super.noSuchMethod(
        Invocation.getter(#orderUpdateStream),
        returnValue: _i3.Stream<Map<String, dynamic>>.empty(),
      ) as _i3.Stream<Map<String, dynamic>>);

  @override
  bool get isWebSocketConnected => (super.noSuchMethod(
        Invocation.getter(#isWebSocketConnected),
        returnValue: false,
      ) as bool);

  @override
  _i3.Stream<String?> get onLinkStream => (super.noSuchMethod(
        Invocation.getter(#onLinkStream),
        returnValue: _i3.Stream<String?>.empty(),
      ) as _i3.Stream<String?>);

  @override
  _i3.Future<void> initialize({dynamic Function(String)? onNotificationTap}) =>
      (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
          {#onNotificationTap: onNotificationTap},
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> showLocalNotification({
    required String? title,
    required String? body,
    String? payload,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #showLocalNotification,
          [],
          {
            #title: title,
            #body: body,
            #payload: payload,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> scheduleDailyFeedbackNotification() => (super.noSuchMethod(
        Invocation.method(
          #scheduleDailyFeedbackNotification,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> sendTestNotification() => (super.noSuchMethod(
        Invocation.method(
          #sendTestNotification,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> getDailyFeedbackEnabled() => (super.noSuchMethod(
        Invocation.method(
          #getDailyFeedbackEnabled,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> setDailyFeedbackEnabled(bool? enabled) =>
      (super.noSuchMethod(
        Invocation.method(
          #setDailyFeedbackEnabled,
          [enabled],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> areNotificationsEnabled() => (super.noSuchMethod(
        Invocation.method(
          #areNotificationsEnabled,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> setNotificationsEnabled(bool? enabled) =>
      (super.noSuchMethod(
        Invocation.method(
          #setNotificationsEnabled,
          [enabled],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i3.Future<String?> getInitialLink() => (super.noSuchMethod(
        Invocation.method(
          #getInitialLink,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);
}
