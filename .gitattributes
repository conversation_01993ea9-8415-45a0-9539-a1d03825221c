*.so filter=lfs diff=lfs merge=lfs -text
*.dill filter=lfs diff=lfs merge=lfs -text
build/app/intermediates/incremental/debug-mergeJavaRes/zip-cache/53QukGNvNpelaGO_lg2Anv9OhU4= filter=lfs diff=lfs merge=lfs -text
build/app/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libflutter.so filter=lfs diff=lfs merge=lfs -text
build/app/intermediates/assets/debug/mergeDebugAssets/flutter_assets/kernel_blob.bin filter=lfs diff=lfs merge=lfs -text
build/app/intermediates/incremental/debug-mergeJavaRes/zip-cache/* filter=lfs diff=lfs merge=lfs -text
