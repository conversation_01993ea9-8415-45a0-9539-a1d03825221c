class CrewPackSelection {
  final String sandwichId;
  final String? bunType;
  final String? heatLevel;
  final double price;

  CrewPackSelection({
    required this.sandwichId,
    this.bunType,
    this.heatLevel,
    required this.price,
  });

  Map<String, dynamic> toMap() {
    return {
      'sandwichId': sandwichId,
      'bunType': bunType,
      'heatLevel': heatLevel,
      'price': price,
    };
  }
}

class CrewPackCustomization {
  final List<CrewPackSelection> selections;
  final int maxSelections;
  final String crewPackType;  // Add crew pack type

  CrewPackCustomization({
    List<CrewPackSelection>? selections,
    this.maxSelections = 3,
    required this.crewPackType,  // Make crew pack type required
  }) : selections = selections ?? [];

  // Return fixed price based on crew pack type
  double get totalPrice {
    double base = 0.0;
    if (crewPackType.toLowerCase().contains('crew pack 1')) {
      base = 45.00;
    } else if (crewPackType.toLowerCase().contains('crew pack 3')) {
      base = 85.00;
    }
    // Add $1 for each sandwich with Brioche Bun
    final bunUpgrades = selections.where((s) => s.bunType == 'Brioche Bun').length;
    return base + (bunUpgrades * 1.0);
  }

  bool canAddMore() => selections.length < maxSelections;

  void addSelection(CrewPackSelection selection) {
    if (canAddMore()) {
      selections.add(selection);
    }
  }

  void removeSelection(int index) {
    if (index >= 0 && index < selections.length) {
      selections.removeAt(index);
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'selections': selections.map((s) => s.toMap()).toList(),
      'totalPrice': totalPrice,
    };
  }
}
