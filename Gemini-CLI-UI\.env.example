# Backend server port
PORT=4008

# Frontend port (Vite)
VITE_PORT=4009

# Gemini CLI path (optional)
# If gemini is not in your PATH, specify the full path here
# Example: GEMINI_PATH=/home/<USER>/.nvm/versions/node/v22.17.0/bin/gemini
# GEMINI_PATH=gemini

# Database file path (optional)
# DATABASE_PATH=./data/database.sqlite

# JWT Secret (required for production)
# Generate a secure random string for production use
JWT_SECRET=your-secret-key-here

# Node environment
NODE_ENV=development