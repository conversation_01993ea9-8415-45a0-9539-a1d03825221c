import 'package:qsr_app/services/notification_service.dart';
import 'dart:async';

class MockNotificationService implements NotificationService {
  @override
  Future<void> initialize({Function(String)? onNotificationTap}) async {
    // Mock initialization
  }

  @override
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    // Mock show local notification
  }

  @override
  Future<void> scheduleDailyFeedbackNotification() async {
    // Mock schedule daily feedback notification
  }

  @override
  Future<void> sendTestNotification() async {
    // Mock send test notification
  }

  @override
  Future<bool> getDailyFeedbackEnabled() async {
    return false; // Mock daily feedback enabled status
  }

  @override
  Future<void> setDailyFeedbackEnabled(bool enabled) async {
    // Mock set daily feedback enabled
  }

  @override
  Future<bool> areNotificationsEnabled() async {
    return true; // Mock notifications enabled status
  }

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    // Mock set notifications enabled
  }

  @override
  Stream<Map<String, dynamic>> get notificationStream => Stream.value({}); // Mock notification stream

  @override
  Stream<Map<String, dynamic>> get orderUpdateStream => Stream.value({}); // Mock order update stream

  @override
  bool get isWebSocketConnected => false; // Mock web socket connection status

  @override
  void dispose() {
    // Mock dispose
  }

  @override
  Future<String?> getInitialLink() async {
    return null; // Mock initial link
  }

  @override
  Stream<String?> get onLinkStream => Stream.value(null); // Mock link stream
}
