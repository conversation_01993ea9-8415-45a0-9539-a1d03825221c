class MenuItem {
  final String id;
  final String name;
  final String description;
  double price;
  final String imageUrl;
  final String category;
  final bool isSpecial;
  final bool available;

  // Sauce selection properties
  bool allowsSauceSelection;
  List<String>? selectedSauces;
  int? includedSauceCount;

  // Bun selection properties
  String? selectedBunType;

  // Heat level properties
  bool allowsHeatLevelSelection;
  String? selectedHeatLevel;

  // Size options
  Map<String, double>? sizes;

  // Crew pack customization properties
  Map<String, int>? customizationCounts;
  List<String>? customizationCategories;
  Map<String, dynamic>? customizations;
  // Nested customization details (e.g., for Crew Packs)
  Map<String, MenuItem>? customizationDetails;

  // Nutrition information
  Map<String, dynamic>? nutritionInfo;

  // Extras system
  bool allowsExtras;
  List<String>? availableExtraCategories;

  MenuItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    required this.category,
    this.isSpecial = false,
    this.available = true,
    this.allowsSauceSelection = false,
    this.selectedSauces,
    this.includedSauceCount,
    this.selectedBunType,
    this.allowsHeatLevelSelection = false,
    this.selectedHeatLevel,
    this.sizes,
    this.customizationCounts,
    this.customizationCategories,
    this.customizations,
    this.nutritionInfo,
    this.allowsExtras = false,
    this.availableExtraCategories,
    this.customizationDetails,
  });

  // Clone method for creating copies of menu items
  MenuItem clone() {
    return MenuItem(
      id: id,
      name: name,
      description: description,
      price: price,
      imageUrl: imageUrl,
      category: category,
      isSpecial: isSpecial,
      available: available,
      allowsSauceSelection: allowsSauceSelection,
      selectedSauces: selectedSauces != null ? List.from(selectedSauces!) : null,
      includedSauceCount: includedSauceCount,
      selectedBunType: selectedBunType,
      allowsHeatLevelSelection: allowsHeatLevelSelection,
      selectedHeatLevel: selectedHeatLevel,
      sizes: sizes != null ? Map.from(sizes!) : null,
      customizationCounts: customizationCounts != null ? Map.from(customizationCounts!) : null,
      customizationCategories: customizationCategories != null ? List.from(customizationCategories!) : null,
      customizations: customizations != null ? Map.from(customizations!) : null,
      nutritionInfo: nutritionInfo != null ? Map.from(nutritionInfo!) : null,
      allowsExtras: allowsExtras,
      availableExtraCategories: availableExtraCategories != null ? List.from(availableExtraCategories!) : null,
      customizationDetails: customizationDetails?.map((k, v) => MapEntry(k, v.clone())),
    );
  }

  // Factory constructor for creating MenuItem from JSON
  factory MenuItem.fromJson(Map<String, dynamic> json) {
    return MenuItem(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      price: json['price'].toDouble(),
      imageUrl: json['imageUrl'],
      category: json['category'],
      isSpecial: json['isSpecial'] ?? false,
      available: json['available'] ?? true,
      allowsSauceSelection: json['allowsSauceSelection'] ?? false,
      selectedSauces: json['selectedSauces'] != null
          ? List<String>.from(json['selectedSauces'])
          : null,
      includedSauceCount: json['includedSauceCount'],
      selectedBunType: json['selectedBunType'],
      allowsHeatLevelSelection: json['allowsHeatLevelSelection'] ?? false,
      selectedHeatLevel: json['selectedHeatLevel'],
      sizes: json['sizes'] != null
          ? Map<String, double>.from(json['sizes'].map((k, v) => MapEntry(k, v.toDouble())))
          : null,
      customizationCounts: json['customizationCounts'] != null
          ? Map<String, int>.from(json['customizationCounts'])
          : null,
      customizationCategories: json['customizationCategories'] != null
          ? List<String>.from(json['customizationCategories'])
          : null,
      customizations: json['customizations'],
      nutritionInfo: json['nutritionInfo'],
    allowsExtras: json['allowsExtras'] ?? false,
    availableExtraCategories: json['availableExtraCategories'] != null
      ? List<String>.from(json['availableExtraCategories'])
      : null,
    customizationDetails: json['customizationDetails'] != null
      ? (json['customizationDetails'] as Map<String, dynamic>).map((k, v) => MapEntry(k, MenuItem.fromJson(v)))
      : null,
  );
  }

  // Convert MenuItem to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'imageUrl': imageUrl,
      'category': category,
      'isSpecial': isSpecial,
      'available': available,
      'allowsSauceSelection': allowsSauceSelection,
      'selectedSauces': selectedSauces,
      'includedSauceCount': includedSauceCount,
      'selectedBunType': selectedBunType,
      'allowsHeatLevelSelection': allowsHeatLevelSelection,
      'selectedHeatLevel': selectedHeatLevel,
      'sizes': sizes,
      'customizationCounts': customizationCounts,
      'customizationCategories': customizationCategories,
      'customizations': customizations,
      'nutritionInfo': nutritionInfo,
      'allowsExtras': allowsExtras,
      'availableExtraCategories': availableExtraCategories,
      'customizationDetails': customizationDetails?.map((k, v) => MapEntry(k, v.toJson())),
    };
  }

  // Heat level management methods
  void setHeatLevel(String? level) {
    if (!allowsHeatLevelSelection) return;
    
    selectedHeatLevel = level;
    customizations ??= {};
    customizations!['selectedHeatLevel'] = level;
    customizations!['displayHeatLevel'] = true;
    
    // If this is a customization detail in a crew pack, update parent
    if (category == 'Whole Wings' && customizations!['isCrewPackItem'] == true) {
      customizations!['heatLevelUpdated'] = true;
    }
  }

  void resetHeatLevel() {
    setHeatLevel(null);
    if (customizations != null) {
      customizations!['displayHeatLevel'] = false;
    }
  }

  List<String> getAvailableHeatLevels() {
    return customizations?['heatLevels'] as List<String>? ?? 
           ['No Heat', 'Mild', 'Medium', 'Hot', 'Extra Hot'];
  }

  String? getSelectedHeatLevel() {
    return selectedHeatLevel ?? customizations?['selectedHeatLevel'];
  }

  String? getHeatLevelDetails(String? level) {
    if (level == null || customizations == null || 
        !customizations!.containsKey('heatLevelDetails')) {
      return null;
    }
    return (customizations!['heatLevelDetails'] as Map<String, String>)[level];
  }

  bool canResetHeatLevel() {
    return allowsHeatLevelSelection && 
           (selectedHeatLevel != null || customizations?['selectedHeatLevel'] != null);
  }

  bool shouldDisplayHeatLevel() {
    return customizations?['displayHeatLevel'] == true;
  }

  bool shouldShowHeatLevelInfo() {
    return customizations?['showHeatLevelInfo'] == true;
  }
}
