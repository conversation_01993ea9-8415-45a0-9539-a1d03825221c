import 'package:flutter/material.dart';
import '../models/menu_item.dart';
import '../models/daily_deal.dart';
import '../services/cart_service.dart';
import '../widgets/deal_card.dart';
import '../constants/colors.dart';
import 'order_type_selection_screen.dart';

class HotDealsPage extends StatelessWidget {
  final List<DailyDeal> dailyDeals;
  final List<MenuItem> regularDeals;
  final CartService cartService;
  final String? title;
  final TextStyle? titleTextStyle;
  final double titleFontSize;

  /// Easily adjust the AppBar title font size by changing [titleFontSize].
  /// If [titleTextStyle] is provided, it will override [titleFontSize].
  const HotDealsPage({
    Key? key,
    required this.dailyDeals,
    required this.regularDeals,
    required this.cartService,
    this.title = 'Hot Deals & Specials',
    this.titleTextStyle,
    this.titleFontSize = 24,
  }) : super(key: key);

  int _getDayNumber(String day) {
    switch (day.toLowerCase()) {
      case 'monday':
        return 1;
      case 'tuesday':
        return 2;
      case 'wednesday':
        return 3;
      case 'thursday':
        return 4;
      case 'friday':
        return 5;
      case 'saturday':
        return 6;
      case 'sunday':
        return 7;
      default:
        return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(0),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Colors.white.withAlpha(128),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 20,
            ),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          title!,
          style: titleTextStyle ??
              const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Color(0xFF7A3B00),
              ),
        ),
        backgroundColor: AppColors.chicaOrange,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              ...dailyDeals.map((deal) {
                final currentDay = DateTime.now().weekday;
                final dealDay = _getDayNumber(deal.dayOfWeek);
                final isToday = currentDay == dealDay;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: DealCard(
                    deal: MenuItem(
                      id: deal.id,
                      name: deal.name,
                      description: '${deal.dayOfWeek} Special: ${deal.description}',
                      price: deal.price,
                      category: deal.category,
                      imageUrl: deal.imageUrl,
                      isSpecial: true,
                      available: isToday,
                    ),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const OrderTypeSelectionScreen(),
                        ),
                      );
                    },
                    index: 0,
                  ),
                );
              }).toList(),
              const SizedBox(height: 64),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withValues(alpha: 0.8),
                      Colors.white.withValues(alpha: 0.5),
                    ],
                  ),
                  border: Border.all(
                    color: Colors.grey.withValues(alpha: 0.0),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'images/CC-Whole Chicken_Orange.png',
                      width: 40,
                      height: 40,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Other Hot Deals',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w900,
                            fontFamily: 'MontserratBlack',
                            fontSize: 25,
                            color: Colors.green,
                          ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
              ...regularDeals.map((deal) => Padding(
                padding: const EdgeInsets.only(bottom: 100),
                child: DealCard(
                  deal: deal,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const OrderTypeSelectionScreen(),
                      ),
                    );
                  },
                  index: 0,
                ),
              )).toList(),
            ],
          ),
        ),
      ),
    );
  }
}
