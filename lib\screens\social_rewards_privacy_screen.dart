import 'package:flutter/material.dart';

/// 🔒 Social Rewards Privacy Screen - GDPR/CCPA compliant privacy information
/// 
/// This screen explains how the Social Rewards feature handles user data,
/// specifically designed to be compliant with privacy laws like GDPR and CCPA.
/// Perfect for a 10-year-old developer to understand privacy requirements!
class SocialRewardsPrivacyScreen extends StatelessWidget {
  const SocialRewardsPrivacyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '🔒 SOCIAL REWARDS PRIVACY',
          style: TextStyle(
            fontFamily: 'MontserratBlack',
            fontWeight: FontWeight.w900,
          ),
        ),
        backgroundColor: const Color(0xFFFF5C22),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 🛡️ Privacy Shield Icon
            Center(
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.privacy_tip,
                  size: 40,
                  color: Colors.green,
                ),
              ),
            ),
            const SizedBox(height: 20),
            
            // 📋 Main Privacy Notice
            const Text(
              'Your Privacy is Important to Us!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                fontFamily: 'MontserratBlack',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            _buildPrivacySection(
              icon: Icons.camera_alt,
              title: 'What We Do With Your Photos & Videos',
              content: [
                '• We DO NOT store your photos or videos on our servers',
                '• Your media stays on your device until you share it',
                '• Photos/videos are shared directly to your chosen social media app',
                '• We never upload your content to Chica\'s Chicken servers',
                '• You have complete control over what you share and where',
              ],
            ),
            
            _buildPrivacySection(
              icon: Icons.stars,
              title: 'Loyalty Points & Rewards',
              content: [
                '• We track that you shared content to award loyalty points',
                '• We store your point balance locally on your device',
                '• When Firebase is enabled, points sync to your account',
                '• You can delete your account and all points at any time',
                '• Points data is used only for the loyalty program',
              ],
            ),
            
            _buildPrivacySection(
              icon: Icons.security,
              title: 'Camera & Device Permissions',
              content: [
                '• Camera access is only used when you tap "Take Photo/Video"',
                '• We request permission before accessing your camera',
                '• You can revoke camera permission anytime in device settings',
                '• No background camera access - only when you\'re using the feature',
                '• Microphone is only used for video recording (if you choose video)',
              ],
            ),
            
            _buildPrivacySection(
              icon: Icons.share,
              title: 'Social Media Sharing',
              content: [
                '• We use your device\'s built-in sharing system',
                '• We don\'t access your social media accounts directly',
                '• We don\'t see what you post or your social media data',
                '• Sharing happens through your device\'s share menu',
                '• Each social media app handles your content according to their privacy policy',
              ],
            ),
            
            _buildPrivacySection(
              icon: Icons.child_care,
              title: 'Children\'s Privacy (Under 13)',
              content: [
                '• Parents should supervise children using this feature',
                '• We don\'t knowingly collect personal data from children under 13',
                '• Parents can disable camera access in device settings',
                '• All sharing requires active user action - nothing is automatic',
                '• Parents should review social media privacy settings',
              ],
            ),
            
            _buildPrivacySection(
              icon: Icons.location_on,
              title: 'Location & Other Data',
              content: [
                '• We don\'t access your location for Social Rewards',
                '• We don\'t read metadata from your photos (like location)',
                '• We don\'t access your contacts or other personal data',
                '• Only camera and photo library permissions are used',
              ],
            ),
            
            _buildPrivacySection(
              icon: Icons.gavel,
              title: 'Your Rights (GDPR/CCPA)',
              content: [
                '• Right to know what data we collect (very minimal for this feature)',
                '• Right to delete your data (delete the app or your account)',
                '• Right to opt-out (don\'t use the Social Rewards feature)',
                '• Right to data portability (export your loyalty points)',
                '• Right to correct inaccurate data (contact us)',
              ],
            ),
            
            const SizedBox(height: 30),
            
            // 📞 Contact Information
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '📞 Questions About Privacy?',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Contact Chica\'s Chicken:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '📧 Email: <EMAIL>',
                    style: TextStyle(color: Colors.blue),
                  ),
                  Text(
                    '📞 Phone: (*************',
                    style: TextStyle(color: Colors.blue),
                  ),
                  Text(
                    '📍 Address: 2853 Dundas Street West, Toronto, ON M6P 2K6',
                    style: TextStyle(color: Colors.blue),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            // ✅ Acknowledgment Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF5C22),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  '✅ I Understand - Continue to Social Rewards',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 10),
            
            const Text(
              'Last updated: $_lastUpdated',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 📋 Helper method to build privacy sections
  Widget _buildPrivacySection({
    required IconData icon,
    required String title,
    required List<String> content,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: const Color(0xFFFF5C22),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...content.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              item,
              style: const TextStyle(
                fontSize: 14,
                height: 1.4,
              ),
            ),
          )),
        ],
      ),
    );
  }

  // 📅 Last updated date - update this when you make changes
  static const String _lastUpdated = 'December 30, 2024';
}
