#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;

/// 🔍 Comprehensive Accessibility Audit Script for CHICA'S Chicken
/// Generates detailed WCAG 2.1 AA compliance report with remediation suggestions
/// 
/// Usage: dart run scripts/accessibility_audit.dart
/// Output: accessibility_report.html and accessibility_report.json

void main(List<String> args) async {
  print('🔍 Starting CHICA\'S Chicken Accessibility Audit...\n');
  
  final auditor = AccessibilityAuditor();
  await auditor.runFullAudit();
  
  print('\n✅ Accessibility audit completed!');
  print('📊 Report generated: accessibility_report.html');
  print('📋 JSON data: accessibility_report.json');
}

class AccessibilityAuditor {
  final List<AccessibilityIssue> issues = [];
  final Map<String, dynamic> auditResults = {};
  
  Future<void> runFullAudit() async {
    print('📋 Running comprehensive accessibility audit...\n');
    
    // 1. Code Analysis
    await _analyzeCodeForAccessibilityIssues();
    
    // 2. Color Contrast Analysis
    await _analyzeColorContrast();
    
    // 3. Widget Structure Analysis
    await _analyzeWidgetStructure();
    
    // 4. Asset Analysis
    await _analyzeAssets();
    
    // 5. Generate Reports
    await _generateReports();
  }
  
  Future<void> _analyzeCodeForAccessibilityIssues() async {
    print('🔍 Analyzing code for accessibility issues...');
    
    final libDir = Directory('lib');
    if (!libDir.existsSync()) {
      print('❌ lib directory not found');
      return;
    }
    
    await for (final file in libDir.list(recursive: true)) {
      if (file is File && file.path.endsWith('.dart')) {
        await _analyzeFile(file);
      }
    }
    
    print('✅ Code analysis completed\n');
  }
  
  Future<void> _analyzeFile(File file) async {
    final content = await file.readAsString();
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final lineNumber = i + 1;
      
      // Check for missing semantic labels
      if (line.contains('Image(') || line.contains('Image.network(') || line.contains('Image.asset(')) {
        if (!line.contains('semanticLabel:')) {
          issues.add(AccessibilityIssue(
            type: IssueType.missingSemanticLabel,
            severity: IssueSeverity.high,
            wcagCriterion: '1.1.1',
            file: file.path,
            line: lineNumber,
            description: 'Image missing semanticLabel',
            remediation: 'Add semanticLabel parameter to Image widget',
            codeExample: 'Image.network(url, semanticLabel: "Description of image")',
          ));
        }
      }
      
      // Check for buttons without semantic labels
      if ((line.contains('ElevatedButton(') || line.contains('TextButton(') || line.contains('OutlinedButton(')) 
          && !line.contains('Semantics(')) {
        issues.add(AccessibilityIssue(
          type: IssueType.missingSemanticLabel,
          severity: IssueSeverity.medium,
          wcagCriterion: '4.1.2',
          file: file.path,
          line: lineNumber,
          description: 'Button may need semantic label for screen readers',
          remediation: 'Wrap button in Semantics widget or use WCAGButton',
          codeExample: 'Semantics(label: "Button description", child: ElevatedButton(...))',
        ));
      }
      
      // Check for hardcoded colors
      if (line.contains('Color(0x') && !line.contains('// WCAG')) {
        issues.add(AccessibilityIssue(
          type: IssueType.colorContrast,
          severity: IssueSeverity.medium,
          wcagCriterion: '1.4.3',
          file: file.path,
          line: lineNumber,
          description: 'Hardcoded color may not meet contrast requirements',
          remediation: 'Use ColorContrastValidator to ensure WCAG compliance',
          codeExample: 'ColorContrastValidator.getAccessibleForegroundColor(backgroundColor)',
        ));
      }
      
      // Check for missing form labels
      if (line.contains('TextFormField(') && !line.contains('labelText:') && !line.contains('AccessibleFormField')) {
        issues.add(AccessibilityIssue(
          type: IssueType.missingFormLabel,
          severity: IssueSeverity.high,
          wcagCriterion: '3.3.2',
          file: file.path,
          line: lineNumber,
          description: 'Form field missing label',
          remediation: 'Add labelText or use AccessibleFormField',
          codeExample: 'AccessibleFormField(label: "Field name", ...)',
        ));
      }
      
      // Check for missing focus management
      if (line.contains('Navigator.push') && !line.contains('FocusScope')) {
        issues.add(AccessibilityIssue(
          type: IssueType.focusManagement,
          severity: IssueSeverity.low,
          wcagCriterion: '2.4.3',
          file: file.path,
          line: lineNumber,
          description: 'Navigation may need focus management',
          remediation: 'Consider focus management after navigation',
          codeExample: 'FocusScope.of(context).requestFocus(focusNode)',
        ));
      }
    }
  }
  
  Future<void> _analyzeColorContrast() async {
    print('🎨 Analyzing color contrast...');
    
    // Define app colors
    final appColors = {
      'chicaOrange': '0xFFFF5C22',
      'chicaRed': '0xFF9B1C24',
      'chicaGold': '0xFFB89B5E',
    };
    
    // Check contrast ratios
    for (final colorEntry in appColors.entries) {
      final colorName = colorEntry.key;
      final colorValue = colorEntry.value;
      
      // This would be expanded to actually calculate contrast ratios
      // For now, we'll add placeholder issues
      issues.add(AccessibilityIssue(
        type: IssueType.colorContrast,
        severity: IssueSeverity.medium,
        wcagCriterion: '1.4.3',
        file: 'Color Palette',
        line: 0,
        description: 'Verify $colorName meets WCAG AA contrast requirements',
        remediation: 'Test color combinations with ColorContrastValidator',
        codeExample: 'ColorContrastValidator.meetsWCAGAA(foreground, background)',
      ));
    }
    
    print('✅ Color contrast analysis completed\n');
  }
  
  Future<void> _analyzeWidgetStructure() async {
    print('🏗️ Analyzing widget structure...');
    
    // Check for proper semantic structure
    final screenFiles = [
      'lib/screens/login_screen.dart',
      'lib/screens/signup_screen.dart',
      'lib/screens/menu_screen.dart',
      'lib/screens/cart_screen.dart',
    ];
    
    for (final screenPath in screenFiles) {
      final file = File(screenPath);
      if (file.existsSync()) {
        final content = await file.readAsString();
        
        // Check for heading structure
        if (!content.contains('Semantics(') || !content.contains('header: true')) {
          issues.add(AccessibilityIssue(
            type: IssueType.semanticStructure,
            severity: IssueSeverity.medium,
            wcagCriterion: '1.3.1',
            file: screenPath,
            line: 0,
            description: 'Screen may be missing proper heading structure',
            remediation: 'Add semantic headings with Semantics(header: true, ...)',
            codeExample: 'Semantics(header: true, child: Text("Screen Title"))',
          ));
        }
        
        // Check for live regions
        if (content.contains('error') && !content.contains('liveRegion: true')) {
          issues.add(AccessibilityIssue(
            type: IssueType.missingLiveRegion,
            severity: IssueSeverity.high,
            wcagCriterion: '4.1.3',
            file: screenPath,
            line: 0,
            description: 'Error messages should be announced to screen readers',
            remediation: 'Wrap error messages in Semantics(liveRegion: true, ...)',
            codeExample: 'Semantics(liveRegion: true, child: Text(errorMessage))',
          ));
        }
      }
    }
    
    print('✅ Widget structure analysis completed\n');
  }
  
  Future<void> _analyzeAssets() async {
    print('📁 Analyzing assets...');
    
    final assetsDir = Directory('assets');
    if (!assetsDir.existsSync()) {
      print('⚠️ Assets directory not found');
      return;
    }
    
    await for (final file in assetsDir.list(recursive: true)) {
      if (file is File) {
        final extension = path.extension(file.path).toLowerCase();
        
        if (['.png', '.jpg', '.jpeg', '.gif', '.svg'].contains(extension)) {
          // Check if image is referenced with semantic labels
          issues.add(AccessibilityIssue(
            type: IssueType.assetAccessibility,
            severity: IssueSeverity.low,
            wcagCriterion: '1.1.1',
            file: file.path,
            line: 0,
            description: 'Verify image has appropriate alt text when used',
            remediation: 'Ensure all image references include semanticLabel',
            codeExample: 'Image.asset("${file.path}", semanticLabel: "Description")',
          ));
        }
      }
    }
    
    print('✅ Asset analysis completed\n');
  }
  
  Future<void> _generateReports() async {
    print('📊 Generating accessibility reports...');
    
    // Generate summary statistics
    final summary = _generateSummary();
    
    // Generate HTML report
    await _generateHtmlReport(summary);
    
    // Generate JSON report
    await _generateJsonReport(summary);
    
    print('✅ Reports generated successfully\n');
  }
  
  Map<String, dynamic> _generateSummary() {
    final summary = <String, dynamic>{
      'totalIssues': issues.length,
      'highSeverity': issues.where((i) => i.severity == IssueSeverity.high).length,
      'mediumSeverity': issues.where((i) => i.severity == IssueSeverity.medium).length,
      'lowSeverity': issues.where((i) => i.severity == IssueSeverity.low).length,
      'wcagCriteria': <String, int>{},
      'issueTypes': <String, int>{},
      'auditDate': DateTime.now().toIso8601String(),
    };
    
    // Count by WCAG criteria
    for (final issue in issues) {
      summary['wcagCriteria'][issue.wcagCriterion] = 
          (summary['wcagCriteria'][issue.wcagCriterion] ?? 0) + 1;
      summary['issueTypes'][issue.type.toString()] = 
          (summary['issueTypes'][issue.type.toString()] ?? 0) + 1;
    }
    
    return summary;
  }
  
  Future<void> _generateHtmlReport(Map<String, dynamic> summary) async {
    final html = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHICA'S Chicken - Accessibility Audit Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #FF5C22; color: white; padding: 20px; border-radius: 8px; }
        .summary { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 8px; }
        .issue { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 8px; }
        .high { border-left: 5px solid #d32f2f; }
        .medium { border-left: 5px solid #f57c00; }
        .low { border-left: 5px solid #388e3c; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; }
        .wcag { background: #e3f2fd; padding: 5px 10px; border-radius: 4px; display: inline-block; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🍗 CHICA'S Chicken Accessibility Audit Report</h1>
        <p>WCAG 2.1 AA Compliance Assessment</p>
        <p>Generated: ${DateTime.now().toString()}</p>
    </div>
    
    <div class="summary">
        <h2>📊 Summary</h2>
        <ul>
            <li><strong>Total Issues:</strong> ${summary['totalIssues']}</li>
            <li><strong>High Severity:</strong> ${summary['highSeverity']}</li>
            <li><strong>Medium Severity:</strong> ${summary['mediumSeverity']}</li>
            <li><strong>Low Severity:</strong> ${summary['lowSeverity']}</li>
        </ul>
    </div>
    
    <h2>🔍 Detailed Issues</h2>
    ${_generateIssueHtml()}
    
    <div class="summary">
        <h2>📋 WCAG 2.1 AA Compliance Checklist</h2>
        <p>Review each criterion and ensure compliance:</p>
        <ul>
            <li><strong>1.1.1 Non-text Content:</strong> All images have alt text</li>
            <li><strong>1.3.1 Info and Relationships:</strong> Proper semantic structure</li>
            <li><strong>1.4.3 Contrast (Minimum):</strong> 4.5:1 contrast ratio for normal text</li>
            <li><strong>2.1.1 Keyboard:</strong> All functionality available via keyboard</li>
            <li><strong>2.4.7 Focus Visible:</strong> Focus indicators are visible</li>
            <li><strong>2.5.5 Target Size:</strong> Touch targets are at least 44x44px</li>
            <li><strong>3.3.2 Labels or Instructions:</strong> Form fields have labels</li>
            <li><strong>4.1.2 Name, Role, Value:</strong> UI components have accessible names</li>
        </ul>
    </div>
</body>
</html>
    ''';
    
    await File('accessibility_report.html').writeAsString(html);
  }
  
  String _generateIssueHtml() {
    return issues.map((issue) => '''
    <div class="issue ${issue.severity.toString().split('.').last}">
        <h3>${issue.description}</h3>
        <div class="wcag">WCAG ${issue.wcagCriterion}</div>
        <p><strong>File:</strong> ${issue.file}:${issue.line}</p>
        <p><strong>Severity:</strong> ${issue.severity.toString().split('.').last.toUpperCase()}</p>
        <p><strong>Remediation:</strong> ${issue.remediation}</p>
        <div class="code">${issue.codeExample}</div>
    </div>
    ''').join('\n');
  }
  
  Future<void> _generateJsonReport(Map<String, dynamic> summary) async {
    final report = {
      'summary': summary,
      'issues': issues.map((issue) => issue.toJson()).toList(),
    };
    
    await File('accessibility_report.json').writeAsString(
      const JsonEncoder.withIndent('  ').convert(report)
    );
  }
}

enum IssueType {
  missingSemanticLabel,
  colorContrast,
  missingFormLabel,
  focusManagement,
  semanticStructure,
  missingLiveRegion,
  assetAccessibility,
}

enum IssueSeverity { high, medium, low }

class AccessibilityIssue {
  final IssueType type;
  final IssueSeverity severity;
  final String wcagCriterion;
  final String file;
  final int line;
  final String description;
  final String remediation;
  final String codeExample;
  
  AccessibilityIssue({
    required this.type,
    required this.severity,
    required this.wcagCriterion,
    required this.file,
    required this.line,
    required this.description,
    required this.remediation,
    required this.codeExample,
  });
  
  Map<String, dynamic> toJson() => {
    'type': type.toString(),
    'severity': severity.toString(),
    'wcagCriterion': wcagCriterion,
    'file': file,
    'line': line,
    'description': description,
    'remediation': remediation,
    'codeExample': codeExample,
  };
}
