import 'package:flutter/material.dart';
import '../models/cart.dart';
import '../models/menu_extras.dart';
import '../models/combo_selection.dart';
import '../models/crew_pack_selection.dart';
import '../models/menu_item.dart';
import '../services/cart_service.dart';
import '../services/menu_service.dart';
import '../screens/combo_selection_screen.dart';

class CartItemEditDialog extends StatefulWidget {
  final CartItem cartItem;
  final int itemIndex;
  final CartService cartService;
  final VoidCallback onUpdated;

  const CartItemEditDialog({
    super.key,
    required this.cartItem,
    required this.itemIndex,
    required this.cartService,
    required this.onUpdated,
  });

  @override
  State<CartItemEditDialog> createState() => _CartItemEditDialogState();
}

class _CartItemEditDialogState extends State<CartItemEditDialog> {
  late int _quantity;
  String? _selectedSize;
  List<String> _selectedSauces = [];
  MenuItemExtras? _extras;
  ComboMeal? _comboMeal;

  // Crew pack editing state
  CrewPackCustomization? _crewPackCustomization;
  Map<String, List<MenuItem>>? _customizations;
  Map<String, List<dynamic>>? _dynamicCustomizations;
  final MenuService _menuService = MenuService();
  final Map<String, List<MenuItem>> _categoryItems = {};
  bool _isLoadingCategories = false;

  // Available sauces (same as in SauceSelectionDialog)
  final List<String> _availableSauces = [
    "Chica's Sauce (Buttermilk Ranch)",
    'Sweet Heat',
    'Buffalo',
    'Hot Honey',
    'Chipotle Aioli',
    'BBQ',
  ];

  @override
  void initState() {
    super.initState();
    _quantity = widget.cartItem.quantity;

    // Initialize selected size - check combo meal first, then regular cart item
    if (widget.cartItem.comboMeal != null) {
      // For combo meals, get bun selection from combo's main item
      _selectedSize = widget.cartItem.comboMeal!.mainItem.selectedBunType;
    } else {
      // For regular items, use cart item's selected size
      _selectedSize = widget.cartItem.selectedSize;
    }

    _selectedSauces = List.from(widget.cartItem.menuItem.selectedSauces ?? []);
    _extras = widget.cartItem.extras?.clone();
    _comboMeal = widget.cartItem.comboMeal;

    // Initialize crew pack data if this is a crew pack
    if (widget.cartItem.crewPackCustomization != null) {
      _crewPackCustomization = _cloneCrewPackCustomization(widget.cartItem.crewPackCustomization!);
    }
    if (widget.cartItem.customizations != null) {
      _customizations = _cloneCustomizations(widget.cartItem.customizations!);
    }
    if (widget.cartItem.dynamicCustomizations != null) {
      _dynamicCustomizations = _cloneDynamicCustomizations(widget.cartItem.dynamicCustomizations!);
    }

    // Load category items for crew pack editing
    if (_isCrewPack) {
      _loadCategoryItems();
    }
  }

  String _getBunDisplayText(String bunKey, double bunPrice) {
    // Convert bun key to proper display text
    String bunName;
    switch (bunKey.toLowerCase()) {
      case 'texas':
      case 'texas toast':
        bunName = 'Texas Toast';
        break;
      case 'brioche':
      case 'brioche bun':
        bunName = 'Brioche Bun';
        break;
      default:
        bunName = bunKey;
        break;
    }

    // Calculate cost based on the lowest price option (Texas Toast is the base)
    final sizes = widget.cartItem.menuItem.sizes!;
    final lowestPrice = sizes.values.reduce((a, b) => a < b ? a : b);
    final extraCost = bunPrice - lowestPrice;

    // Add cost information
    if (extraCost > 0) {
      return '$bunName (+\$${extraCost.toStringAsFixed(2)})';
    } else {
      return '$bunName (Included)';
    }
  }

  List<Widget> _buildExtrasSection() {
    if (_extras == null) return [];

    List<Widget> widgets = [];

    for (final section in _extras!.sections) {
      final selectedExtras = _extras!.getSelectedExtrasForSection(section.id);

      if (section.extras.isNotEmpty) {
        widgets.add(
          Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  section.title.toUpperCase(),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                ...section.extras.map((extra) {
                  final selectedExtra = selectedExtras.firstWhere(
                    (selected) => selected.extra.id == extra.id,
                    orElse: () => SelectedExtra(extra: extra, quantity: 0),
                  );
                  final isSelected = selectedExtra.quantity > 0;

                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                extra.name,
                                style: const TextStyle(fontWeight: FontWeight.w500),
                              ),
                              Text(
                                '+\$${extra.price.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  color: Colors.deepOrange,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              onPressed: isSelected
                                  ? () => _updateExtraQuantity(section, extra, selectedExtra.quantity - 1)
                                  : null,
                              icon: const Icon(Icons.remove_circle_outline),
                              iconSize: 20,
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                '${selectedExtra.quantity}',
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ),
                            IconButton(
                              onPressed: () => _updateExtraQuantity(section, extra, selectedExtra.quantity + 1),
                              icon: const Icon(Icons.add_circle_outline),
                              iconSize: 20,
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        );
      }
    }

    return widgets;
  }

  void _updateExtraQuantity(MenuExtraSection section, MenuExtra extra, int quantity) {
    if (_extras == null) return;

    // Check if this is a combo extra
    if (extra.id == 'combo_upgrade' && quantity > 0) {
      _handleComboSelection();
      return;
    }

    setState(() {
      if (quantity <= 0) {
        _extras!.removeExtra(section.id, extra.id);
      } else {
        _extras!.updateExtraQuantity(section.id, extra.id, quantity);
        if (!_extras!.getSelectedExtrasForSection(section.id).any((selected) => selected.extra.id == extra.id)) {
          _extras!.addExtra(section.id, extra, quantity: quantity);
        }
      }
    });
  }

  /// Handle combo selection by navigating to combo selection screen
  Future<void> _handleComboSelection() async {
    // Create a combo with the current menu item and selected size
    final combo = ComboConfiguration.createCombo(widget.cartItem.menuItem, selectedSize: _selectedSize);

    final result = await Navigator.of(context).push<ComboMeal>(
      MaterialPageRoute(
        builder: (context) => ComboSelectionScreen(combo: combo),
      ),
    );

    if (result != null && mounted) {
      // Store the combo selection
      setState(() {
        _comboMeal = result;
        // Remove the combo extra from selection since it's now configured
        _extras?.removeExtra('combo', 'combo_upgrade');
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Combo configured! You can now update the item.'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  Widget _buildComboSection() {
    if (_comboMeal == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.orange.withValues(alpha: 0.1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Main: ${_comboMeal!.mainItem.name}',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          // Show bun selection for sandwiches
          if (_comboMeal!.mainItem.category.toLowerCase().contains('sandwich') &&
              _comboMeal!.mainItem.selectedBunType != null) ...[
            const SizedBox(height: 4),
            Text(
              'Bun: ${_comboMeal!.mainItem.selectedBunType}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
          if (_comboMeal!.selectedDrink != null) ...[
            const SizedBox(height: 4),
            Text(
              'Drink: ${_comboMeal!.selectedDrink!.name}${_comboMeal!.selectedDrinkSize != null ? ' (${_comboMeal!.selectedDrinkSize})' : ''}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
          if (_comboMeal!.selectedSide != null) ...[
            const SizedBox(height: 4),
            Text(
              'Side: ${_comboMeal!.selectedSide!.name}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
          const SizedBox(height: 8),
          Text(
            'Total: \$${_comboMeal!.totalPrice.toStringAsFixed(2)}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _editCombo() async {
    if (_comboMeal == null) return;

    final result = await Navigator.of(context).push<ComboMeal>(
      MaterialPageRoute(
        builder: (context) => ComboSelectionScreen(combo: _comboMeal!),
      ),
    );

    if (result != null && mounted) {
      setState(() {
        _comboMeal = result;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'EDIT ${widget.cartItem.menuItem.name.toUpperCase()}',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quantity selector
            const Text(
              'QUANTITY',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: _quantity > 1 ? () => setState(() => _quantity--) : null,
                  icon: const Icon(Icons.remove_circle_outline),
                  iconSize: 32,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _quantity.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => setState(() => _quantity++),
                  icon: const Icon(Icons.add_circle_outline),
                  iconSize: 32,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Size selector (if applicable)
            if (widget.cartItem.menuItem.sizes != null && widget.cartItem.menuItem.sizes!.isNotEmpty) ...[
              Text(
                widget.cartItem.menuItem.category.toLowerCase() == 'sandwiches' 
                  ? 'CHOOSE BUN' 
                  : 'CHOOSE SIZE',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              ...widget.cartItem.menuItem.sizes!.entries.map((entry) {
                final isSelected = _selectedSize == entry.key;
                // Calculate extra cost properly for sandwiches (bun upgrades)
                double extraCost;
                if (widget.cartItem.menuItem.category.toLowerCase().contains('sandwich')) {
                  // For sandwiches, calculate bun upgrade cost
                  final lowestPrice = widget.cartItem.menuItem.sizes!.values.reduce((a, b) => a < b ? a : b);
                  extraCost = entry.value - lowestPrice;
                } else {
                  // For other items, use size price vs base price
                  extraCost = entry.value - widget.cartItem.menuItem.price;
                }
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: InkWell(
                    onTap: () => setState(() => _selectedSize = entry.key),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: isSelected ? Colors.deepOrange : Colors.grey,
                          width: isSelected ? 2 : 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        color: isSelected ? Colors.deepOrange.withValues(alpha: 0.1) : null,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              widget.cartItem.menuItem.category.toLowerCase() == 'sandwiches'
                                ? _getBunDisplayText(entry.key, entry.value)
                                : '${entry.key.toUpperCase()} ${extraCost > 0 ? '(+\$${extraCost.toStringAsFixed(2)})' : '(FREE)'}',
                              style: TextStyle(
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                color: isSelected ? Colors.deepOrange : Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
              const SizedBox(height: 16),
            ],

            // Sauce selector (if applicable)
            if (widget.cartItem.menuItem.allowsSauceSelection) ...[
              const Text(
                'SAUCES',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              ..._availableSauces.map((sauce) {
                final isSelected = _selectedSauces.contains(sauce);
                return CheckboxListTile(
                  title: Text(sauce.toUpperCase()),
                  value: isSelected,
                  onChanged: (bool? value) {
                    setState(() {
                      if (value == true) {
                        // Check if we have a sauce limit
                        if (widget.cartItem.menuItem.includedSauceCount != null) {
                          if (_selectedSauces.length < widget.cartItem.menuItem.includedSauceCount!) {
                            _selectedSauces.add(sauce);
                          } else {
                            // Replace the first sauce if at limit
                            _selectedSauces.removeAt(0);
                            _selectedSauces.add(sauce);
                          }
                        } else {
                          _selectedSauces.add(sauce);
                        }
                      } else {
                        _selectedSauces.remove(sauce);
                      }
                    });
                  },
                  activeColor: Colors.deepOrange,
                );
              }).toList(),
            ],

            // Combo section (if applicable)
            if (_comboMeal != null) ...[
              const SizedBox(height: 16),
              const Text(
                'COMBO DETAILS',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              _buildComboSection(),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: _editCombo,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('EDIT COMBO SELECTIONS'),
              ),
            ],

            // Crew Pack Editing section (if applicable)
            if (_isCrewPack) ...[
              const SizedBox(height: 16),
              const Text(
                'CREW PACK SELECTIONS',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              if (_isLoadingCategories)
                const Center(
                  child: CircularProgressIndicator(),
                )
              else
                ..._buildCrewPackEditingSection(),
            ],

            // Extras section (if applicable)
            if (_extras != null && _extras!.sections.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'EXTRAS',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              ..._buildExtrasSection(),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('CANCEL'),
        ),
        ElevatedButton(
          onPressed: _updateCartItem,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.deepOrange,
            foregroundColor: Colors.white,
          ),
          child: const Text('UPDATE'),
        ),
      ],
    );
  }

  void _updateCartItem() {
    // Update the menu item's selected sauces
    widget.cartItem.menuItem.selectedSauces = _selectedSauces.isNotEmpty ? _selectedSauces : null;

    // Update quantity
    widget.cartService.updateCartItemQuantity(widget.itemIndex, _quantity);

    // Update size/bun selection
    if (widget.cartItem.comboMeal != null) {
      // For combo meals, update the bun selection in both the combo's main item AND the cart item's menu item
      final currentBunType = widget.cartItem.comboMeal!.mainItem.selectedBunType;
      if (_selectedSize != currentBunType) {
        widget.cartItem.comboMeal!.mainItem.selectedBunType = _selectedSize;
        // Also update the cart item's menu item for consistent display
        widget.cartItem.menuItem.selectedBunType = _selectedSize;
        // Update the combo meal reference to trigger price recalculation
        widget.cartItem.comboMeal = widget.cartItem.comboMeal;
      }
    } else {
      // For regular items, update the cart item's selected size
      if (_selectedSize != widget.cartItem.selectedSize) {
        widget.cartService.updateCartItem(widget.itemIndex, selectedSize: _selectedSize);
      }
    }

    // Update extras if changed
    if (_extras != null) {
      widget.cartItem.extras = _extras;
    }

    // Update combo if changed
    if (_comboMeal != null) {
      widget.cartItem.comboMeal = _comboMeal;
    }

    // Update crew pack customizations if changed
    if (_isCrewPack) {
      widget.cartItem.crewPackCustomization = _crewPackCustomization;
      widget.cartItem.customizations = _customizations;
      widget.cartItem.dynamicCustomizations = _dynamicCustomizations;
    }

    widget.onUpdated();
    Navigator.of(context).pop();
  }

  // 🍗 Crew Pack Editing UI

  /// Build crew pack editing section
  List<Widget> _buildCrewPackEditingSection() {
    if (!_isCrewPack) return [];

    List<Widget> widgets = [];

    // Build sections for each category
    for (String category in widget.cartItem.menuItem.customizationCategories ?? []) {
      widgets.add(_buildCrewPackCategorySection(category));
      widgets.add(const SizedBox(height: 12));
    }

    return widgets;
  }

  /// Build a single category section for crew pack editing
  Widget _buildCrewPackCategorySection(String category) {
    final maxCount = widget.cartItem.menuItem.customizationCounts?[category] ?? 0;
    final currentCount = _getCurrentSelectionCount(category);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.withValues(alpha: 0.05),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                category.toUpperCase(),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              Text(
                '$currentCount / $maxCount',
                style: TextStyle(
                  fontSize: 12,
                  color: currentCount == maxCount ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Show current selections
          ..._buildCurrentSelections(category),

          // Show available items to add
          if (currentCount < maxCount) ...[
            const Divider(),
            const Text(
              'Add Items:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            ..._buildAvailableItems(category),
          ],
        ],
      ),
    );
  }

  /// Build current selections for a category
  List<Widget> _buildCurrentSelections(String category) {
    List<Widget> widgets = [];

    if (category == 'Sandwiches' && _crewPackCustomization != null) {
      // Handle sandwich selections
      for (int i = 0; i < _crewPackCustomization!.selections.length; i++) {
        final selection = _crewPackCustomization!.selections[i];
        widgets.add(_buildSandwichSelectionTile(selection, i));
      }
    } else {
      // Handle other category selections
      final items = _customizations?[category] ?? [];
      final dynamicItems = _dynamicCustomizations?[category] ?? [];

      // Regular items
      for (int i = 0; i < items.length; i++) {
        widgets.add(_buildRegularSelectionTile(category, items[i], i, false));
      }

      // Dynamic items (with heat levels)
      for (int i = 0; i < dynamicItems.length; i++) {
        widgets.add(_buildDynamicSelectionTile(category, dynamicItems[i], i));
      }
    }

    return widgets;
  }

  /// Build available items for a category
  List<Widget> _buildAvailableItems(String category) {
    final availableItems = _categoryItems[category] ?? [];

    return availableItems.map((item) {
      return ListTile(
        dense: true,
        title: Text(
          item.name,
          style: const TextStyle(fontSize: 12),
        ),
        trailing: ElevatedButton(
          onPressed: () => _addItemToCrewPack(category, item),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.deepOrange,
            foregroundColor: Colors.white,
            minimumSize: const Size(60, 30),
          ),
          child: const Text(
            'Add',
            style: TextStyle(fontSize: 10),
          ),
        ),
      );
    }).toList();
  }

  // 🍗 Crew Pack Helper Methods

  /// Check if this cart item is a crew pack
  bool get _isCrewPack => widget.cartItem.menuItem.category.toLowerCase().contains('crew pack');

  /// Clone crew pack customization for editing
  CrewPackCustomization _cloneCrewPackCustomization(CrewPackCustomization original) {
    final cloned = CrewPackCustomization(maxSelections: original.maxSelections, crewPackType: original.crewPackType);
    for (final selection in original.selections) {
      cloned.addSelection(CrewPackSelection(
        sandwichId: selection.sandwichId,
        bunType: selection.bunType,
        heatLevel: selection.heatLevel,
        price: selection.price,
      ));
    }
    return cloned;
  }

  /// Clone customizations map for editing
  Map<String, List<MenuItem>> _cloneCustomizations(Map<String, List<MenuItem>> original) {
    final Map<String, List<MenuItem>> cloned = {};
    for (final entry in original.entries) {
      cloned[entry.key] = List<MenuItem>.from(entry.value);
    }
    return cloned;
  }

  /// Clone dynamic customizations map for editing
  Map<String, List<dynamic>> _cloneDynamicCustomizations(Map<String, List<dynamic>> original) {
    final Map<String, List<dynamic>> cloned = {};
    for (final entry in original.entries) {
      cloned[entry.key] = List<dynamic>.from(entry.value);
    }
    return cloned;
  }

  /// Load category items for crew pack editing
  Future<void> _loadCategoryItems() async {
    if (!_isCrewPack) return;

    setState(() {
      _isLoadingCategories = true;
    });

    try {
      for (String category in widget.cartItem.menuItem.customizationCategories ?? []) {
        final items = await _menuService.getMenuItems(category);
        _categoryItems[category] = items;
      }
    } catch (e) {
      debugPrint('Error loading category items: $e');
    }

    setState(() {
      _isLoadingCategories = false;
    });
  }

  /// Get current selection count for a category
  int _getCurrentSelectionCount(String category) {
    if (category == 'Sandwiches') {
      return _crewPackCustomization?.selections.length ?? 0;
    } else {
      final regularCount = _customizations?[category]?.length ?? 0;
      final dynamicCount = _dynamicCustomizations?[category]?.length ?? 0;
      return regularCount + dynamicCount;
    }
  }

  /// Build sandwich selection tile
  Widget _buildSandwichSelectionTile(CrewPackSelection selection, int index) {
    final sandwich = _categoryItems['Sandwiches']?.firstWhere(
      (item) => item.id == selection.sandwichId,
      orElse: () => MenuItem(
        id: selection.sandwichId,
        name: 'Unknown Sandwich',
        description: '',
        price: 0,
        imageUrl: '',
        category: 'Sandwiches',
      ),
    );

    return ListTile(
      dense: true,
      title: Text(
        sandwich?.name ?? 'Unknown Sandwich',
        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        'Bun: ${selection.bunType}, Heat: ${selection.heatLevel}',
        style: const TextStyle(fontSize: 10),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: () => _editSandwichSelection(index),
            icon: const Icon(Icons.edit, size: 16),
            style: IconButton.styleFrom(
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              foregroundColor: Colors.blue,
            ),
          ),
          IconButton(
            onPressed: () => _removeSandwichSelection(index),
            icon: const Icon(Icons.remove_circle_outline, size: 16),
            style: IconButton.styleFrom(
              backgroundColor: Colors.red.withValues(alpha: 0.1),
              foregroundColor: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// Build regular selection tile
  Widget _buildRegularSelectionTile(String category, MenuItem item, int index, bool isDynamic) {
    return ListTile(
      dense: true,
      title: Text(
        item.name,
        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
      trailing: IconButton(
        onPressed: () => _removeRegularSelection(category, index),
        icon: const Icon(Icons.remove_circle_outline, size: 16),
        style: IconButton.styleFrom(
          backgroundColor: Colors.red.withValues(alpha: 0.1),
          foregroundColor: Colors.red,
        ),
      ),
    );
  }

  /// Build dynamic selection tile (with heat level)
  Widget _buildDynamicSelectionTile(String category, dynamic selection, int index) {
    final MenuItem item = selection is Map ? selection['item'] : selection;
    final String? heatLevel = selection is Map ? selection['heatLevel'] : null;

    return ListTile(
      dense: true,
      title: Text(
        item.name,
        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
      subtitle: heatLevel != null
          ? Text(
              'Heat: $heatLevel',
              style: const TextStyle(fontSize: 10),
            )
          : null,
      trailing: IconButton(
        onPressed: () => _removeDynamicSelection(category, index),
        icon: const Icon(Icons.remove_circle_outline, size: 16),
        style: IconButton.styleFrom(
          backgroundColor: Colors.red.withValues(alpha: 0.1),
          foregroundColor: Colors.red,
        ),
      ),
    );
  }

  // 🍗 Crew Pack Action Methods

  /// Add item to crew pack
  Future<void> _addItemToCrewPack(String category, MenuItem item) async {
    if (category == 'Sandwiches') {
      await _addSandwichToCrewPack(item);
    } else if (item.allowsHeatLevelSelection == true) {
      await _addItemWithHeatLevel(category, item);
    } else {
      _addRegularItem(category, item);
    }
  }

  /// Add sandwich to crew pack with bun and heat level selection
  Future<void> _addSandwichToCrewPack(MenuItem sandwich) async {
    final List<String> availableBuns = ['Regular Bun', 'Brioche Bun'];
    final List<String> availableHeatLevels = ['Plain', 'Mild', 'Medium', 'Hot', 'HOT AF'];

    // Select bun type
    final String? selectedBun = await showDialog<String>(
      context: context,
      builder: (context) => SimpleDialog(
        title: Text('Select Bun for ${sandwich.name}'),
        children: availableBuns.map((bun) {
          return SimpleDialogOption(
            onPressed: () => Navigator.pop(context, bun),
            child: Text(bun == 'Brioche Bun' ? 'Brioche Bun (+\$1.00)' : bun),
          );
        }).toList(),
      ),
    );

    if (selectedBun == null || !mounted) return;

    // Select heat level
    final String? selectedHeatLevel = await showDialog<String>(
      context: context,
      builder: (context) => SimpleDialog(
        title: Text('Select Heat Level for ${sandwich.name}'),
        children: availableHeatLevels.map((heat) {
          return SimpleDialogOption(
            onPressed: () => Navigator.pop(context, heat),
            child: Text(heat),
          );
        }).toList(),
      ),
    );

    if (selectedHeatLevel == null || !mounted) return;

    // Add to crew pack customization
    setState(() {
      _crewPackCustomization ??= CrewPackCustomization(maxSelections: 10, crewPackType: widget.cartItem.menuItem.category);
      final bunPrice = selectedBun == 'Brioche Bun' ? 1.0 : 0.0;
      _crewPackCustomization!.addSelection(
        CrewPackSelection(
          sandwichId: sandwich.id,
          bunType: selectedBun,
          heatLevel: selectedHeatLevel,
          price: sandwich.price + bunPrice,
        ),
      );
    });
  }

  /// Add item with heat level selection
  Future<void> _addItemWithHeatLevel(String category, MenuItem item) async {
    final List<String> availableHeatLevels = ['Plain', 'Mild', 'Medium', 'Hot', 'HOT AF'];

    final String? selectedHeatLevel = await showDialog<String>(
      context: context,
      builder: (context) => SimpleDialog(
        title: Text('Select Heat Level for ${item.name}'),
        children: availableHeatLevels.map((heat) {
          return SimpleDialogOption(
            onPressed: () => Navigator.pop(context, heat),
            child: Text(heat),
          );
        }).toList(),
      ),
    );

    if (selectedHeatLevel == null || !mounted) return;

    setState(() {
      _dynamicCustomizations ??= {};
      _dynamicCustomizations![category] ??= [];
      _dynamicCustomizations![category]!.add({
        'item': item,
        'heatLevel': selectedHeatLevel,
      });
    });
  }

  /// Add regular item (no special options)
  void _addRegularItem(String category, MenuItem item) {
    setState(() {
      _customizations ??= {};
      _customizations![category] ??= [];
      _customizations![category]!.add(item);
    });
  }

  /// Edit sandwich selection
  Future<void> _editSandwichSelection(int index) async {
    if (_crewPackCustomization == null || index >= _crewPackCustomization!.selections.length) return;

    final selection = _crewPackCustomization!.selections[index];
    final sandwich = _categoryItems['Sandwiches']?.firstWhere(
      (item) => item.id == selection.sandwichId,
      orElse: () => MenuItem(id: '', name: '', description: '', price: 0, imageUrl: '', category: ''),
    );

    if (sandwich?.id.isEmpty ?? true) return;

    await _addSandwichToCrewPack(sandwich!);
    _removeSandwichSelection(index);
  }

  /// Remove sandwich selection
  void _removeSandwichSelection(int index) {
    setState(() {
      _crewPackCustomization?.removeSelection(index);
    });
  }

  /// Remove regular selection
  void _removeRegularSelection(String category, int index) {
    setState(() {
      _customizations?[category]?.removeAt(index);
      if (_customizations?[category]?.isEmpty ?? false) {
        _customizations?.remove(category);
      }
    });
  }

  /// Remove dynamic selection
  void _removeDynamicSelection(String category, int index) {
    setState(() {
      _dynamicCustomizations?[category]?.removeAt(index);
      if (_dynamicCustomizations?[category]?.isEmpty ?? false) {
        _dynamicCustomizations?.remove(category);
      }
    });
  }
}
