// 🔔 Abstract Notification Service for Chica's Chicken App
// Defines the interface for all notification services (local, mobile, web)

import 'dart:async';

abstract class NotificationService {
  Future<void> initialize({Function(String)? onNotificationTap});
  Future<void> showLocalNotification({required String title, required String body, String? payload});
  Future<void> scheduleDailyFeedbackNotification();
  Future<void> sendTestNotification();
  Future<bool> getDailyFeedbackEnabled();
  Future<void> setDailyFeedbackEnabled(bool enabled);
  Future<bool> areNotificationsEnabled();
  Future<void> setNotificationsEnabled(bool enabled);
  Stream<Map<String, dynamic>> get notificationStream;
  Stream<Map<String, dynamic>> get orderUpdateStream;
  bool get isWebSocketConnected;
  void dispose();
  Future<String?> getInitialLink();
  Stream<String?> get onLinkStream;
}
