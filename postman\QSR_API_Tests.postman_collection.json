{"info": {"name": "QSR API Tests - Chica's Chicken", "description": "Comprehensive API test suite for the QSR ordering system", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set base URL if not already set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'https://api.chicaschicken.com');", "}"]}}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has user data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('user');", "    pm.expect(responseJson).to.have.property('token');", "});", "", "// Store token for subsequent requests", "if (pm.response.code === 201) {", "    const responseJson = pm.response.json();", "    pm.environment.set('auth_token', responseJson.token);", "    pm.environment.set('user_id', responseJson.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"TestPassword123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"phone\": \"+1234567890\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has auth token', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('token');", "    pm.expect(responseJson.token).to.be.a('string');", "});", "", "// Store token", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    pm.environment.set('auth_token', responseJson.token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"TestPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has new token', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('token');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "auth", "refresh"]}}}]}, {"name": "Menu Management", "item": [{"name": "Get Menu Categories", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has categories array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('categories');", "    pm.expect(responseJson.categories).to.be.an('array');", "});", "", "pm.test('Categories have required fields', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.categories.length > 0) {", "        const category = responseJson.categories[0];", "        pm.expect(category).to.have.property('id');", "        pm.expect(category).to.have.property('name');", "        pm.expect(category).to.have.property('description');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/menu/categories", "host": ["{{base_url}}"], "path": ["api", "menu", "categories"]}}}, {"name": "Get Menu Items", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has items array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('items');", "    pm.expect(responseJson.items).to.be.an('array');", "});", "", "pm.test('Items have required fields', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.items.length > 0) {", "        const item = responseJson.items[0];", "        pm.expect(item).to.have.property('id');", "        pm.expect(item).to.have.property('name');", "        pm.expect(item).to.have.property('price');", "        pm.expect(item).to.have.property('category');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/menu/items?category=sandwiches", "host": ["{{base_url}}"], "path": ["api", "menu", "items"], "query": [{"key": "category", "value": "sandwiches"}]}}}, {"name": "Search Menu Items", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Search returns relevant results', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('results');", "    pm.expect(responseJson.results).to.be.an('array');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/menu/search?q=chicken", "host": ["{{base_url}}"], "path": ["api", "menu", "search"], "query": [{"key": "q", "value": "chicken"}]}}}]}, {"name": "Order Processing", "item": [{"name": "Create Order", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has order data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('order');", "    pm.expect(responseJson.order).to.have.property('id');", "    pm.expect(responseJson.order).to.have.property('status');", "});", "", "// Store order ID for subsequent tests", "if (pm.response.code === 201) {", "    const responseJson = pm.response.json();", "    pm.environment.set('order_id', responseJson.order.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"menuItemId\": \"sandwich_001\",\n      \"quantity\": 1,\n      \"customizations\": {\n        \"heatLevel\": \"medium\",\n        \"sauces\": [\"chipotle\", \"ranch\"],\n        \"bunType\": \"brioche\"\n      }\n    }\n  ],\n  \"orderType\": \"pickup\",\n  \"specialInstructions\": \"Extra crispy please\"\n}"}, "url": {"raw": "{{base_url}}/api/orders", "host": ["{{base_url}}"], "path": ["api", "orders"]}}}, {"name": "Get Order Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has order status', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('order');", "    pm.expect(responseJson.order).to.have.property('status');", "});"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/orders/{{order_id}}", "host": ["{{base_url}}"], "path": ["api", "orders", "{{order_id}}"]}}}, {"name": "Update Order", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Order updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "});"]}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"specialInstructions\": \"Updated: Extra crispy and well done\"\n}"}, "url": {"raw": "{{base_url}}/api/orders/{{order_id}}", "host": ["{{base_url}}"], "path": ["api", "orders", "{{order_id}}"]}}}]}, {"name": "Payment Processing", "item": [{"name": "Process Payment", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Payment processed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('transaction');", "    pm.expect(responseJson.transaction).to.have.property('status', 'completed');", "});", "", "// Store transaction ID", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    pm.environment.set('transaction_id', responseJson.transaction.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"orderId\": \"{{order_id}}\",\n  \"paymentMethod\": {\n    \"type\": \"credit_card\",\n    \"token\": \"tok_visa_test\"\n  },\n  \"amount\": 1299,\n  \"currency\": \"CAD\"\n}"}, "url": {"raw": "{{base_url}}/api/payments/process", "host": ["{{base_url}}"], "path": ["api", "payments", "process"]}}}]}, {"name": "Loyalty Program", "item": [{"name": "Get Loyalty Account", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has loyalty data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('account');", "    pm.expect(responseJson.account).to.have.property('currentPoints');", "    pm.expect(responseJson.account).to.have.property('tier');", "});"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/loyalty/account", "host": ["{{base_url}}"], "path": ["api", "loyalty", "account"]}}}, {"name": "Redeem Points", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Points redeemed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(response<PERSON><PERSON>).to.have.property('newBalance');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"points\": 100,\n  \"rewardId\": \"free_side\"\n}"}, "url": {"raw": "{{base_url}}/api/loyalty/redeem", "host": ["{{base_url}}"], "path": ["api", "loyalty", "redeem"]}}}]}], "variable": [{"key": "base_url", "value": "https://api.chicaschicken.com", "type": "string"}]}