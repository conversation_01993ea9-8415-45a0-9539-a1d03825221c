@echo off
echo 🛒 TESTING CREW PACK CART CUSTOMIZATION FIX
echo.
echo This will launch the app to test the crew pack cart fix
echo.

echo 📦 Getting dependencies...
flutter pub get
echo.

echo 🌐 Launching on Chrome...
echo.
echo 🧪 TESTING CHECKLIST - CREW PACK CART FIX:
echo.
echo ✅ STEP 1: Navigate to Crew Packs
echo    1. Go to Menu → Crew Packs
echo    2. Select any crew pack (e.g., "Crew Pack 1")
echo.
echo ✅ STEP 2: Customize the Crew Pack
echo    1. Choose sandwiches with DIFFERENT heat levels
echo    2. Select chicken bites with a heat level
echo    3. Choose SPECIFIC sides (not just default)
echo    4. Select SPECIFIC drinks
echo    5. Add sauces if available
echo    6. Click "CUSTOMIZE PACK"
echo.
echo ✅ STEP 3: Verify Cart Details
echo    1. Go to Cart
echo    2. Check that you see:
echo       - All sandwich selections with heat levels
echo       - Chicken bites with heat level (red text)
echo       - Specific sides chosen (not "2x Sides [R]")
echo       - Specific drinks chosen (not "2x Drinks")
echo       - Category icons for each item
echo       - Complete customization breakdown
echo.
echo ✅ STEP 4: Test Cart Edit
echo    1. Click edit button on crew pack
echo    2. Verify all customizations are preserved
echo    3. Make changes and save
echo    4. Verify changes appear in cart
echo.
echo 🎯 SUCCESS CRITERIA:
echo    - Cart shows ALL customization details
echo    - No more generic "2x Sides [R]" or "2x Drinks"
echo    - Heat levels displayed in red text
echo    - Complete transparency of user selections
echo.

flutter run -d chrome --web-renderer html
