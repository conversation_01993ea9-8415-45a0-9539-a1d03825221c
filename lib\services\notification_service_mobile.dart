// 📱 Mobile-Only Notification Service for Chica's Chicken App
// This service handles all notification functionality for mobile platforms
// with full Firebase and local notification support

import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../utils/logger.dart';
import '../firebase_options.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Platform-specific instances
  FirebaseMessaging? _messaging;
  FlutterLocalNotificationsPlugin? _localNotifications;
  
  // Cross-platform navigation callback
  Function(String)? _onNotificationTap;
  
  // Stream controllers for notification events
  final StreamController<String> _notificationTapController = StreamController<String>.broadcast();
  Stream<String> get onNotificationTap => _notificationTapController.stream;

  // 🚀 Initialize the notification service
  Future<void> initialize({Function(String)? onNotificationTap}) async {
    try {
      AppLogger.info('🔔 Initializing Mobile Notification Service...');
      
      _onNotificationTap = onNotificationTap;
      
      // Mobile-only initialization
      await _initializeMobile();
      
      AppLogger.info('✅ Mobile Notification Service initialized successfully');
    } catch (e) {
      AppLogger.error('❌ Failed to initialize Mobile Notification Service: $e');
    }
  }

  // 📱 Mobile-specific initialization
  Future<void> _initializeMobile() async {
    try {
      // Initialize timezone data
      tz_data.initializeTimeZones();
      
      // Initialize Firebase
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
      }
      
      // Initialize local notifications
      _localNotifications = FlutterLocalNotificationsPlugin();
      
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );
      
      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );
      
      await _localNotifications!.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );
      
      // Initialize Firebase Messaging
      _messaging = FirebaseMessaging.instance;
      
      // Request permissions
      NotificationSettings settings = await _messaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );
      
      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        AppLogger.info('✅ Notification permissions granted');
        
        // Get FCM token
        String? token = await _messaging!.getToken();
        AppLogger.info('📱 FCM Token: $token');
        
        // Set up message handlers
        FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
        FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
        
        // Handle notification when app is launched from terminated state
        RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
        if (initialMessage != null) {
          _handleNotificationTap(initialMessage);
        }
      } else {
        AppLogger.warning('⚠️ Notification permissions denied');
      }
      
    } catch (e) {
      AppLogger.error('❌ Mobile initialization failed: $e');
      rethrow;
    }
  }

  // 📨 Handle notification tap (mobile only)
  void _onNotificationTapped(NotificationResponse response) {
    try {
      String? payload = response.payload;
      AppLogger.info('🔔 Local notification tapped with payload: $payload');
      _notificationTapController.add(payload ?? '/home');
      _onNotificationTap?.call(payload ?? '/home');
        } catch (e) {
      AppLogger.error('❌ Error handling notification tap: $e');
    }
  }

  // 📱 Handle foreground Firebase message (mobile only)
  void _handleForegroundMessage(RemoteMessage message) {
    try {
      AppLogger.info('📨 Received foreground message: ${message.notification?.title}');
      
      // Show local notification for foreground messages
      _showLocalNotification(
        title: message.notification?.title ?? 'Chica\'s Chicken',
        body: message.notification?.body ?? 'You have a new message!',
        payload: message.data['route'] ?? '/home',
      );
    } catch (e) {
      AppLogger.error('❌ Error handling foreground message: $e');
    }
  }

  // 🔗 Handle notification tap from Firebase (mobile only)
  void _handleNotificationTap(RemoteMessage message) {
    try {
      String route = message.data['route'] ?? '/home';
      AppLogger.info('🔗 Firebase notification tapped, navigating to: $route');
      
      _notificationTapController.add(route);
      _onNotificationTap?.call(route);
    } catch (e) {
      AppLogger.error('❌ Error handling Firebase notification tap: $e');
    }
  }

  // 📅 Schedule daily feedback notification (mobile only)
  Future<void> scheduleDailyFeedbackNotification() async {
    try {
      // Cancel any existing notification
      await _localNotifications!.cancel(1);
      
      // Get current time and set to 6 PM today
      final now = DateTime.now();
      var scheduledTime = DateTime(now.year, now.month, now.day, 18, 0); // 6 PM
      
      // If 6 PM has passed today, schedule for tomorrow
      if (scheduledTime.isBefore(now)) {
        scheduledTime = scheduledTime.add(const Duration(days: 1));
      }
      
      final tz.TZDateTime tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.local);
      
      const androidDetails = AndroidNotificationDetails(
        'daily_feedback',
        'Daily Feedback Reminders',
        channelDescription: 'Daily reminders to share feedback',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );
      
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );
      
      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );
      
      // Schedule the notification
      await _localNotifications!.zonedSchedule(
        1, // Notification ID
        '🌟 Loved your meal?', // Title
        'Share your feedback and earn rewards! Tap to visit your loyalty page.', // Body
        tzScheduledTime,
        notificationDetails,
        payload: '/loyalty', // This tells the app where to go when tapped
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time, // Repeat daily at the same time
      );
      
      AppLogger.info('✅ Daily feedback notification scheduled for ${scheduledTime.toString()}');
    } catch (e) {
      AppLogger.error('❌ Failed to schedule daily feedback notification: $e');
    }
  }

  // 📨 Show a local notification immediately (mobile only)
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'instant_notifications',
        'Instant Notifications',
        channelDescription: 'Immediate notifications',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications!.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000), // Unique ID
        title,
        body,
        notificationDetails,
        payload: payload,
      );
    } catch (e) {
      AppLogger.error('❌ Failed to show local notification: $e');
    }
  }

  // 🧪 Send a test notification (mobile only)
  Future<void> sendTestNotification() async {
    await _showLocalNotification(
      title: '🧪 Test Notification',
      body: 'This is a test notification from Chica\'s Chicken!',
      payload: '/loyalty',
    );
  }

  // ⚙️ User Preferences (Cross-platform)

  // Get daily feedback notification preference
  Future<bool> getDailyFeedbackEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('daily_feedback_enabled') ?? true;
  }

  // Set daily feedback notification preference
  Future<void> setDailyFeedbackEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('daily_feedback_enabled', enabled);

    if (enabled) {
      await scheduleDailyFeedbackNotification();
    } else {
      await _localNotifications?.cancel(1);
    }

    AppLogger.info('📱 Daily feedback notifications ${enabled ? 'enabled' : 'disabled'}');
  }

  // Check if notifications are enabled (for notification settings screen)
  Future<bool> areNotificationsEnabled() async {
    return await getDailyFeedbackEnabled();
  }

  // Set notifications enabled (for notification settings screen)
  Future<void> setNotificationsEnabled(bool enabled) async {
    await setDailyFeedbackEnabled(enabled);
  }

  // Stream getters for notification banner and test screen
  Stream<Map<String, dynamic>> get notificationStream =>
      _notificationTapController.stream.map((route) => {'route': route});

  Stream<Map<String, dynamic>> get orderUpdateStream =>
      _notificationTapController.stream.map((route) => {'route': route});

  // WebSocket connection status (mobile - always false, no WebSocket)
  bool get isWebSocketConnected => false;

  // 🧹 Cleanup
  void dispose() {
    _notificationTapController.close();
  }
}
