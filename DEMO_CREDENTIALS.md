# 🔐 Demo Credentials for CHICA'S Chicken Authentication System

## Overview
The authentication system is currently running with a mock service for web compatibility. This allows you to test all authentication features without requiring Firebase setup.

## 🧪 Test Credentials

### Demo Account (Pre-created)
- **Email:** `<EMAIL>`
- **Password:** `demo_password`
- **Status:** Pre-verified account with 100 loyalty points

### Create New Account
You can create a new account with any email and password that meets the requirements:

#### Password Requirements
- ✅ Minimum 8 characters
- ✅ At least one uppercase letter (A-Z)
- ✅ At least one lowercase letter (a-z)
- ✅ At least one number (0-9)
- ✅ At least one special character (!@#$%^&*)

#### Example Strong Passwords
- `ChicasRocks123!`
- `MyPassword2024#`
- `SecureLogin456$`
- `FlutterApp789%`

## 🎯 Testing Features

### 1. **Registration Flow**
1. Click "Sign Up" on the login screen
2. Fill in the registration form:
   - Full Name: `Test User`
   - Email: `<EMAIL>`
   - Password: `TestPassword123!`
   - Confirm Password: `TestPassword123!`
3. Check both Terms of Use and Privacy Policy boxes
4. Click "CREATE ACCOUNT"
5. You'll receive a welcome message with 100 bonus points!

### 2. **Login Flow**
1. Use the demo credentials or your newly created account
2. Test the "Remember me" checkbox
3. Try the "Forgot Password?" link

### 3. **Password Reset**
1. Click "Forgot Password?" on login screen
2. Enter any email address
3. Click "SEND RESET LINK"
4. You'll see a success message (mock implementation)

### 4. **Google Sign-In (Mock)**
1. Click "Continue with Google" on login screen
2. This will automatically sign you in as the demo user
3. Simulates the Google OAuth flow

### 5. **Security Features Testing**

#### Account Lockout
1. Try logging in with wrong password 5 times
2. Account will be locked for 15 minutes
3. You'll see a lockout message

#### Session Timeout
- Sessions automatically expire after 30 minutes of inactivity
- You'll be redirected to login screen when session expires

#### Password Strength Indicator
1. Go to signup screen
2. Start typing in the password field
3. Watch the strength indicator change colors:
   - 🔴 **Red:** Weak
   - 🟠 **Orange:** Fair
   - 🟡 **Yellow:** Good
   - 🟢 **Light Green:** Strong
   - 🟢 **Green:** Very Strong

## 🔧 Mock Service Features

### What Works
- ✅ User registration and login
- ✅ Password validation and strength checking
- ✅ Session management and timeout
- ✅ Account lockout protection
- ✅ Password reset flow (UI only)
- ✅ Google Sign-In simulation
- ✅ User profile management
- ✅ Loyalty points system
- ✅ Secure data storage (browser local storage)

### What's Simulated
- 📧 Email verification (auto-verified)
- 📧 Password reset emails (success message only)
- 🔐 Biometric authentication (success on non-web platforms)
- 🌐 Google OAuth (automatic demo user login)

## 🚀 Production Setup

When ready for production, you'll need to:

1. **Enable Firebase Authentication**
   ```yaml
   # In pubspec.yaml, uncomment:
   firebase_core: ^2.24.2
   firebase_auth: ^4.15.3
   cloud_firestore: ^4.13.6
   ```

2. **Update Service References**
   ```dart
   // Replace MockAuthService with AuthService
   import 'services/auth_service.dart';
   ```

3. **Configure Firebase Project**
   - Create Firebase project
   - Enable Authentication providers
   - Set up Firestore database
   - Update firebase_options.dart

4. **Add Real Email Service**
   - Configure email templates
   - Set up SMTP or email service provider
   - Update password reset flow

## 🎮 Interactive Demo

### Quick Test Scenario
1. **Start:** Open the app (should show login screen)
2. **Register:** Create a new account with strong password
3. **Login:** Sign in with your new credentials
4. **Explore:** Navigate through the app
5. **Security:** Try the password reset flow
6. **Logout:** Sign out and try logging back in

### Advanced Testing
1. **Test Account Lockout:** Try wrong password 5 times
2. **Test Session Timeout:** Leave app idle for 30+ minutes
3. **Test Password Strength:** Try various password combinations
4. **Test Form Validation:** Submit forms with invalid data
5. **Test Navigation:** Use browser back/forward buttons

## 📱 Platform Differences

### Web (Current)
- ✅ Full authentication flow
- ❌ No biometric authentication
- ✅ Secure browser storage
- ✅ Session management

### Mobile (When Firebase enabled)
- ✅ Full authentication flow
- ✅ Biometric authentication (fingerprint/face)
- ✅ Secure device storage
- ✅ Push notifications for security alerts

## 🛠️ Troubleshooting

### Common Issues
1. **"Account locked" message:** Wait 15 minutes or clear browser storage
2. **Session expired:** Normal behavior after 30 minutes
3. **Password too weak:** Follow the requirements checklist
4. **Form validation errors:** Check all required fields

### Reset Demo Data
To reset all demo data:
1. Open browser developer tools (F12)
2. Go to Application/Storage tab
3. Clear all local storage and session storage
4. Refresh the page

## 📞 Support

For technical questions about the authentication system:
- Check the `AUTHENTICATION_SYSTEM.md` documentation
- Review the code in `lib/services/mock_auth_service.dart`
- Test with the provided demo credentials

---

**Happy Testing! 🎉**

The authentication system is fully functional and ready for testing. All security features are implemented and working correctly with the mock service.
