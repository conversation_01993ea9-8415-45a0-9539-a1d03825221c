import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:visibility_detector/visibility_detector.dart';
import '../utils/animation_utils.dart';
import '../models/menu_item.dart';
import '../constants/colors.dart';

class AnimatedMenuItemCard extends StatefulWidget {
  final MenuItem item;
  final VoidCallback onTap;
  final VoidCallback onAddToCart;
  final bool isFavorite;
  final ValueChanged<bool> onFavoriteChanged;
  final int index;

  const AnimatedMenuItemCard({
    Key? key,
    required this.item,
    required this.onTap,
    required this.onAddToCart,
    required this.isFavorite,
    required this.onFavoriteChanged,
    required this.index,
  }) : super(key: key);

  @override
  State<AnimatedMenuItemCard> createState() => _AnimatedMenuItemCardState();
}

class _AnimatedMenuItemCardState extends State<AnimatedMenuItemCard> {
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('menu-item-${widget.item.id}'),
      onVisibilityChanged: (info) {
        // Trigger animation when item becomes visible
      },
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovering = true),
        onExit: (_) => setState(() => _isHovering = false),
        child: AnimatedPhysicalModel(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          elevation: _isHovering ? 12 : 4,
          shape: BoxShape.rectangle,
          shadowColor: Colors.black,
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(10),
          child: InkWell(
            onTap: widget.onTap,
            borderRadius: BorderRadius.circular(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      child: Image.asset(
                        widget.item.imageUrl,
                        height: 200,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: IconButton(
                        icon: Icon(
                          widget.isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: widget.isFavorite ? Colors.red : Colors.white,
                        ),
                        onPressed: () => widget.onFavoriteChanged(!widget.isFavorite),
                      ).animate(target: widget.isFavorite ? 1 : 0)
                        .scale(
                          duration: AnimationDurations.fast,
                          curve: GSAPCurves.backOut,
                        )
                        .rotate(
                          begin: 0,
                          end: 0.15,
                          duration: AnimationDurations.fast,
                        ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.item.name,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: FoodItemTextColors.itemNameColor,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        widget.item.description,
                        style: TextStyle(
                          color: FoodItemTextColors.itemDescriptionColor,
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '\$${widget.item.price.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: FoodItemTextColors.itemPriceColor,
                            ),
                          ),
                          ElevatedButton(
                            onPressed: widget.onAddToCart,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            child: const Text('Add to Cart'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ).animate()
        .fadeIn(
          delay: Duration(milliseconds: (widget.index * 100)),
          duration: AnimationDurations.normal,
        )
        .slideX(
          begin: 0.25,
          end: 0,
          delay: Duration(milliseconds: (widget.index * 100)),
          duration: AnimationDurations.normal,
          curve: GSAPCurves.backOut,
        ),
    );
  }
}
