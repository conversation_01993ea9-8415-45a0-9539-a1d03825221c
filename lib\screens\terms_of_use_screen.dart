import 'package:flutter/material.dart';

class TermsOfUseScreen extends StatelessWidget {
  const TermsOfUseScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms of Use'),
        backgroundColor: const Color(0xFFFF5C22), // Chica Orange
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Text(
                    'CHICA\'S CHICKEN',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFFFF5C22),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Terms of Use',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Last Updated: ${DateTime.now().toString().split(' ')[0]}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Content sections
            _buildSection(
              context,
              '1. ACCEPTANCE OF TERMS',
              'By accessing or using the CHICA\'S Chicken mobile application, website, or services (collectively, the "Service"), you agree to be bound by these Terms of Use ("Terms"). If you do not agree to these Terms, please do not use our Service.\n\nThese Terms constitute a legally binding agreement between you and CHICA\'S Chicken regarding your use of our Service.',
            ),

            _buildSection(
              context,
              '2. DESCRIPTION OF SERVICE',
              'CHICA\'S Chicken provides a mobile application and online platform that allows users to:\n• Browse our menu and restaurant information\n• Place orders for pickup or delivery\n• Participate in our loyalty rewards program\n• Access promotional offers and discounts\n• Play games and earn rewards\n• Manage account preferences and payment methods',
            ),

            _buildSection(
              context,
              '3. USER ACCOUNTS',
              'To access certain features of our Service, you may be required to create an account. You agree to:\n• Provide accurate, current, and complete information\n• Maintain the security of your password and account\n• Accept responsibility for all activities under your account\n• Notify us immediately of any unauthorized use\n\nYou must be at least 13 years old to create an account. Users under 18 must have parental consent.',
            ),

            _buildSection(
              context,
              '4. ORDERS AND PAYMENTS',
              'When placing orders through our Service:\n• All orders are subject to availability and acceptance\n• Prices are subject to change without notice\n• Payment is required at the time of order\n• We reserve the right to refuse or cancel orders\n• Delivery times are estimates and may vary\n• Additional fees may apply for delivery services',
            ),

            _buildSection(
              context,
              '5. LOYALTY PROGRAM',
              'Our loyalty program allows you to earn points and rewards. Program terms:\n• Points have no cash value\n• Points may expire according to program rules\n• We reserve the right to modify or discontinue the program\n• Fraudulent activity may result in account termination\n• Rewards are subject to availability and restrictions',
            ),

            _buildSection(
              context,
              '6. PROHIBITED USES',
              'You may not use our Service to:\n• Violate any applicable laws or regulations\n• Transmit harmful, offensive, or inappropriate content\n• Interfere with or disrupt the Service\n• Attempt to gain unauthorized access to our systems\n• Use automated systems to access the Service\n• Impersonate others or provide false information',
            ),

            _buildSection(
              context,
              '7. INTELLECTUAL PROPERTY',
              'All content, trademarks, and intellectual property on our Service are owned by CHICA\'S Chicken or our licensors. You may not:\n• Copy, modify, or distribute our content\n• Use our trademarks without permission\n• Reverse engineer our software\n• Create derivative works based on our Service',
            ),

            _buildSection(
              context,
              '8. PRIVACY',
              'Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information. By using our Service, you consent to our privacy practices as described in our Privacy Policy.',
            ),

            _buildSection(
              context,
              '9. DISCLAIMERS',
              'Our Service is provided "as is" without warranties of any kind. We do not guarantee:\n• Uninterrupted or error-free service\n• Accuracy of information\n• Availability of menu items\n• Delivery times\n\nWe are not liable for any indirect, incidental, or consequential damages.',
            ),

            _buildSection(
              context,
              '10. LIMITATION OF LIABILITY',
              'To the maximum extent permitted by Canadian law, CHICA\'S Chicken\'s total liability shall not exceed the amount you paid for the Service in the 12 months preceding the claim.',
            ),

            _buildSection(
              context,
              '11. GOVERNING LAW',
              'These Terms are governed by the laws of Canada and the province in which you reside. Any disputes will be resolved in the courts of competent jurisdiction in Canada.',
            ),

            _buildSection(
              context,
              '12. CHANGES TO TERMS',
              'We may update these Terms at any time. We will notify you of significant changes through the Service or by email. Your continued use of the Service after changes constitutes acceptance of the new Terms.',
            ),

            _buildSection(
              context,
              '13. CONTACT INFORMATION',
              'If you have questions about these Terms, please contact us:\n\nCHICA\'S Chicken Customer Service\nEmail: <EMAIL>\nPhone: 1-800-CHICAS-1\n\nMailing Address:\nCHICA\'S Chicken Legal Department\n[Address to be provided]\nCanada',
            ),

            const SizedBox(height: 32),

            // Footer
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: const Color(0xFFFF5C22).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'By using CHICA\'S Chicken services, you acknowledge that you have read, understood, and agree to be bound by these Terms of Use.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFFFF5C22),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.6,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }
}
