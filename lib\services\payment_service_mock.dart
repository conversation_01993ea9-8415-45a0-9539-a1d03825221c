import 'package:flutter/material.dart';
import '../models/payment_method.dart';

import 'package:qsr_app/services/payment_service.dart';

class PaymentServiceMock implements PaymentService {
  @override
  Future<List<PaymentMethodOption>> getAvailablePaymentMethods() async {
    return [
      PaymentMethodOption(
        id: 'mock_card',
        name: 'Credit/Debit Card (Mock)',
        icon: Icons.credit_card,
        type: PaymentType.card,
        isAvailable: true,
        description: 'For testing purposes',
        color: Colors.blue,
      ),
      PaymentMethodOption(
        id: 'mock_cash',
        name: 'Cash on Pickup (Mock)',
        icon: Icons.money,
        type: PaymentType.cash,
        isAvailable: true,
        description: 'Pay at the counter',
        color: Colors.orange,
      ),
    ];
  }

  @override
  Future<PaymentResult> processPayment({
    required PaymentMethodOption paymentMethod,
    required double amount,
    required String orderId,
    required Map<String, dynamic> orderDetails,
  }) async {
    await Future.delayed(const Duration(seconds: 1));
    return PaymentResult(
      success: true,
      transactionId: 'mock_txn_${DateTime.now().millisecondsSinceEpoch}',
      paymentMethod: paymentMethod.name,
      amount: amount,
    );
  }
}
