{"id": "staging-environment", "name": "QSR API - Staging", "values": [{"key": "base_url", "value": "https://staging-api.chicaschicken.com", "type": "default", "enabled": true}, {"key": "auth_token", "value": "", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "order_id", "value": "", "type": "default", "enabled": true}, {"key": "transaction_id", "value": "", "type": "default", "enabled": true}, {"key": "api_version", "value": "v1", "type": "default", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "test_password", "value": "StagingTest123!", "type": "secret", "enabled": true}, {"key": "stripe_test_key", "value": "pk_test_staging_key_here", "type": "secret", "enabled": true}, {"key": "revel_api_key", "value": "staging_revel_key", "type": "secret", "enabled": true}, {"key": "timeout_ms", "value": "10000", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}